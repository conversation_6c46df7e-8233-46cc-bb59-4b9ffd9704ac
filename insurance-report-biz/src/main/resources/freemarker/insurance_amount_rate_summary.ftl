<#--
{
    "summary":{
        "type":"区域类型：COUNTRY,AREA,DISTRICT,BRANCH",
        "finished":false,
        "insuranceAmountYearRate":"",
        "rankInfos":[
            {
                "name":"",
                "insuranceAmountYearRate":""
            }
        ]
    }
}
-->
<#--异业保费配比完成情况小结-->
<#if summary.type == 'COUNTRY'>
<#--全国-->
<div style="padding: 13px 12px;font-size: 15px;;color: #666;border-radius: 8px;background: rgba(47, 104, 254, 0.05);">
    本年客户合作深度
    <#if summary.finished>
        <span style="font-size: 15px;;color: #666;">${summary.insuranceAmountYearRate}</span>
    <#else>
        <span style="color: #E8380D;">${summary.insuranceAmountYearRate}</span>
    </#if>
    <#if summary.rankInfos?size gt 0>，区域排名靠
        <#if summary.finished>
            前TOP${summary.rankInfos?size}为<span style="font-size: 15px;;color: #666;"><#list summary.rankInfos as rankInfo>${rankInfo.name}（${rankInfo.insuranceAmountYearRate}）<#sep>、</#sep></#list></span>
        <#else>
            后TOP${summary.rankInfos?size}为<span style="color: #E8380D;"><#list summary.rankInfos as rankInfo>${rankInfo.name}（${rankInfo.insuranceAmountYearRate}）<#sep>、</#sep></#list></span>，需重点关注
        </#if>
    </#if>。
</div>
<#elseif summary.type == 'DISTRICT'>
<#--片区-->
<div style="padding: 13px 12px;font-size: 15px;;color: #666;border-radius: 8px;background: rgba(47, 104, 254, 0.05);">
    本年客户合作深度
    <#if summary.finished>
        <span style="font-size: 15px;;color: #666;">${summary.insuranceAmountYearRate}</span>
    <#else>
        <span style="color: #E8380D;">${summary.insuranceAmountYearRate}</span>
    </#if>
    <#if summary.rankInfos?size gt 0>，分支排名靠
        <#if summary.finished>
            前TOP${summary.rankInfos?size}为<span style="font-size: 15px;;color: #666;"><#list summary.rankInfos as rankInfo>${rankInfo.name}（${rankInfo.insuranceAmountYearRate}）<#sep>、</#sep></#list></span>
        <#else>
            后TOP${summary.rankInfos?size}为<span style="color: #E8380D;"><#list summary.rankInfos as rankInfo>${rankInfo.name}（${rankInfo.insuranceAmountYearRate}）<#sep>、</#sep></#list></span>，需重点关注
        </#if>
    </#if>。
</div>
<#elseif summary.type == 'AREA' && existDistrict>
<#--区域-->
<div style="padding: 13px 12px;font-size: 15px;;color: #666;border-radius: 8px;background: rgba(47, 104, 254, 0.05);">
    本年客户合作深度
    <#if summary.finished>
        <span style="font-size: 15px;;color: #666;">${summary.insuranceAmountYearRate}</span>
    <#else>
        <span style="color: #E8380D;">${summary.insuranceAmountYearRate}</span>
    </#if>
    <#if summary.rankInfos?size gt 0>，片区排名靠
        <#if summary.finished>
            前TOP${summary.rankInfos?size}为<span style="font-size: 15px;;color: #666;"><#list summary.rankInfos as rankInfo>${rankInfo.name}（${rankInfo.insuranceAmountYearRate}）<#sep>、</#sep></#list></span>
        <#else>
            后TOP${summary.rankInfos?size}为<span style="color: #E8380D;"><#list summary.rankInfos as rankInfo>${rankInfo.name}（${rankInfo.insuranceAmountYearRate}）<#sep>、</#sep></#list></span>，需重点关注
        </#if>
    </#if>。
</div>
<#elseif summary.type == 'AREA' && !existDistrict>
<#--区域-->
<div style="padding: 13px 12px;font-size: 15px;;color: #666;border-radius: 8px;background: rgba(47, 104, 254, 0.05);">
    本年客户合作深度
    <#if summary.finished>
        <span style="font-size: 15px;;color: #666;">${summary.insuranceAmountYearRate}</span>
    <#else>
        <span style="color: #E8380D;">${summary.insuranceAmountYearRate}</span>
    </#if>
    <#if summary.rankInfos?size gt 0>，分支排名靠
        <#if summary.finished>
            前TOP${summary.rankInfos?size}为<span style="font-size: 15px;;color: #666;"><#list summary.rankInfos as rankInfo>${rankInfo.name}（${rankInfo.insuranceAmountYearRate}）<#sep>、</#sep></#list></span>
        <#else>
            后TOP${summary.rankInfos?size}为<span style="color: #E8380D;"><#list summary.rankInfos as rankInfo>${rankInfo.name}（${rankInfo.insuranceAmountYearRate}）<#sep>、</#sep></#list></span>，需重点关注
        </#if>
    </#if>。
</div>
<#elseif summary.type == 'BRANCH'>
<#--分支-->
<div style="padding: 13px 12px;font-size: 15px;;color: #666;border-radius: 8px;background: rgba(47, 104, 254, 0.05);">
    本年客户合作深度
    <#if summary.finished>
        <span style="font-size: 15px;;color: #666;">${summary.insuranceAmountYearRate}</span>
    <#else>
        <span style="color: #E8380D;">${summary.insuranceAmountYearRate}</span>
    </#if>
    <#if summary.rankInfos?size gt 0>，个人排名靠
        <#if summary.finished>
            前TOP${summary.rankInfos?size}为<span style="font-size: 15px;;color: #666;"><#list summary.rankInfos as rankInfo>${rankInfo.name}（${rankInfo.insuranceAmountYearRate}）<#sep>、</#sep></#list></span>
        <#else>
            后TOP${summary.rankInfos?size}为<span style="color: #E8380D;"><#list summary.rankInfos as rankInfo>${rankInfo.name}（${rankInfo.insuranceAmountYearRate}）<#sep>、</#sep></#list></span>，需重点关注
        </#if>
    </#if>。
</div>
</#if>