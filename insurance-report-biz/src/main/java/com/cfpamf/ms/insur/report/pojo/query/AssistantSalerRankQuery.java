package com.cfpamf.ms.insur.report.pojo.query;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.report.constant.BaseConstants;
import com.cfpamf.ms.insur.report.enums.EnumAssistantAreaDim;
import com.cfpamf.ms.insur.report.enums.EnumAssistantTimeDim;
import com.cfpamf.ms.insur.report.enums.ExcptEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

import static com.cfpamf.ms.insur.report.constant.BaseConstants.VALIDATION_BEFORE_DAY;

@Data
@ApiModel("保险销售助手-排名列表查询")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssistantSalerRankQuery extends PageQuery{

    @ApiModelProperty("时间分区")
    String pt;

    @ApiModelProperty(name = "员工编码", hidden = true)
    String employeeCode;

    @ApiModelProperty("业务类型 assessConvert 标准保费, promotionFee 推广费")
    String businessType;

    /**
     * 数据类型 share 分享, visit
     */
    @ApiModelProperty("时间维度 MONTH-本月，YEAR-本年")
    EnumAssistantTimeDim timeDim;

    /**
     * 排名维度 ALL 全国 AREA 区域 DISTRICT 片区 BRANCH 分支、SUPER 督导、EMP个人
     */
    @NotNull(message = "排名维度不能为空")
    @ApiModelProperty("排名维度 ALL 全国 AREA 区域 DISTRICT 片区 BRANCH 分支、SUPER 督导、EMP个人")
    EnumAssistantAreaDim rankDim;

    /**
     * 获取当前日期前一天的日期字符串，格式为YYYYMMDD。如果已有pt变量且不为空，则直接返回该pt变量的值。
     *
     * @return 返回前一天日期的字符串表示，格式为YYYYMMDD。
     */
    public String getPt() {
        // 判断pt变量是否已存在且不为空，若满足条件则直接返回pt
        if (StringUtils.isNotBlank(pt)) {
            LocalDate parse = LocalDate.parse(pt, BaseConstants.FMT_YYYYMMDD);
            if (parse.isBefore(LocalDate.now().minusDays(VALIDATION_BEFORE_DAY))) {
                throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "pt不合法：" + pt);
            }
            return pt;
        }
        // 若pt为空，则获取当前日期前一天的日期，并以YYYYMMDD格式返回
        return LocalDate.now().minusDays(1L).format(BaseConstants.FMT_YYYYMMDD);
    }
}
