package com.cfpamf.ms.insur.report.service.assistant;

import com.cfpamf.ms.insur.report.enums.DateTypeEnum;
import com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerBasicDTO;
import com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerRankDTO;
import com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerSingleTrendDTO;
import com.cfpamf.ms.insur.report.pojo.po.AdsInsuranceEmpMarketingProgressDfp;
import com.cfpamf.ms.insur.report.pojo.query.AssistantSalerRankQuery;
import com.github.pagehelper.PageInfo;

import java.util.List;

public interface AssistantSalerService {
    public AssistantSalerBasicDTO getAssistantSalerData(String empCode, String pt);

    public AssistantSalerBasicDTO getAssistantSalerRank(String empCode,String bchCode,String areaCode, String pt, DateTypeEnum dateTypeEnum);

    AssistantSalerSingleTrendDTO queryEmpAmtTrend(String empCode, List<String> ptListCurrentYear);

    AssistantSalerSingleTrendDTO queryEmpATypeCustTransRateTrend(String empCode, List<String> ptListCurrentYear);

    AssistantSalerSingleTrendDTO queryEmpATypeCustInsuranceRateTrend(String empCode, List<String> ptListCurrentYear);

    AssistantSalerSingleTrendDTO queryEmpRetentionRate(String empCode, List<String> ptListCurrentYear);

    /**
     * 分页查询标准保费排名
     * @param query 查询参数
     * @return 分页结果
     */
    PageInfo<AssistantSalerRankDTO> pageAssessConvertRank(AssistantSalerRankQuery query);

    /**
     * 分页查询推广费排名
     * @param query 查询参数
     * @return 分页结果
     */
    PageInfo<AssistantSalerRankDTO> pagePromotionRank(AssistantSalerRankQuery query);

    /**
     * 清理排名缓存
     * @param pt 时间分区，如果为null则清理所有排名缓存
     */
    void clearRankCache(String pt);
}
