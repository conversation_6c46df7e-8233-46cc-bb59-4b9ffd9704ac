package com.cfpamf.ms.insur.report.service.behavior.interfaces;

import com.cfpamf.ms.insur.report.pojo.po.BehaviorLogTask;
import com.cfpamf.ms.insur.report.pojo.po.BehaviorLogTaskDetail;

import java.util.List;

/**
 * 用户行为日志任务服务接口
 *
 * <AUTHOR>
 * @date 2025-09-30
 */
public interface BehaviorLogTaskService {

    /**
     * 创建日志拉取任务
     *
     * @param task 任务信息
     * @return 创建的任务
     */
    BehaviorLogTask createTask(BehaviorLogTask task);

    /**
     * 更新任务信息
     *
     * @param task 任务信息
     * @return 更新后的任务
     */
    BehaviorLogTask updateTask(BehaviorLogTask task);

    /**
     * 删除任务
     *
     * @param taskId 任务ID
     * @return 是否删除成功
     */
    boolean deleteTask(Long taskId);

    /**
     * 根据ID查询任务
     *
     * @param taskId 任务ID
     * @return 任务信息
     */
    BehaviorLogTask getTaskById(Long taskId);

    /**
     * 查询所有任务
     *
     * @return 任务列表
     */
    List<BehaviorLogTask> getAllTasks();

    /**
     * 根据状态查询任务
     *
     * @param taskStatus 任务状态
     * @return 任务列表
     */
    List<BehaviorLogTask> getTasksByStatus(Integer taskStatus);

    /**
     * 查询需要执行的任务
     *
     * @return 任务列表
     */
    List<BehaviorLogTask> getExecutableTasks();

    /**
     * 启动任务
     *
     * @param taskId 任务ID
     * @return 是否启动成功
     */
    boolean startTask(Long taskId);

    /**
     * 停止任务
     *
     * @param taskId 任务ID
     * @return 是否停止成功
     */
    boolean stopTask(Long taskId);

    /**
     * 重启任务
     *
     * @param taskId 任务ID
     * @return 是否重启成功
     */
    boolean restartTask(Long taskId);

    /**
     * 获取任务明细列表
     *
     * @param taskId 任务ID
     * @return 任务明细列表
     */
    List<BehaviorLogTaskDetail> getTaskDetails(Long taskId);

    /**
     * 获取任务执行进度
     *
     * @param taskId 任务ID
     * @return 执行进度（0-100）
     */
    int getTaskProgress(Long taskId);

    /**
     * 检查任务是否正在执行
     *
     * @param taskId 任务ID
     * @return 是否正在执行
     */
    boolean isTaskRunning(Long taskId);
}
