package com.cfpamf.ms.insur.report.web;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.report.enums.ExcptEnum;
import com.cfpamf.ms.insur.report.feign.DataAuthFacade;
import com.cfpamf.ms.insur.report.feign.resp.vo.UserDetailVO;
import com.cfpamf.ms.insur.report.pojo.CommonResult;
import com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerRankDTO;
import com.cfpamf.ms.insur.report.pojo.dto.DataAuthDTO;
import com.cfpamf.ms.insur.report.pojo.query.AssistantSalerRankQuery;
import com.cfpamf.ms.insur.report.service.BmsService;
import com.cfpamf.ms.insur.report.service.assistant.AssistantSalerService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Api(value = "AssistantSalerNewController", tags = "销售助手指标新控制器")
@RestController
@RequestMapping("/assistant/saler")
public class AssistantSalerNewController {
    @Autowired
    DataAuthFacade dataAuthFacade;
    @Autowired
    private BmsService bmsService;

    @Autowired
    AssistantSalerService assistantSalerService;

    /**
     * 年度推广费排行榜（BAPP 相关接口）
     * @param query
     * @return
     */
    @ApiOperation(value = "年度推广费排行榜（BAPP 相关接口）")
    @PostMapping("/rank/promotion/year")
    public PageInfo<AssistantSalerRankDTO> pageEmpPromotionRankMonth(@RequestBody AssistantSalerRankQuery query) {

        UserDetailVO userDetail= bmsService.getContextUserDetail();
        CommonResult<DataAuthDTO> commonResult = dataAuthFacade.dataAuth(bmsService.getToken());
        if (userDetail==null || !commonResult.isSuccess()){
            throw new MSBizNormalException(ExcptEnum.PRODUCT_ERROR_201010);
        }
        query.setEmployeeCode(userDetail.getJobNumber());
        DataAuthDTO dataAuthDTO = commonResult.getData();

        return assistantSalerService.pagePromotionRank(query);
    }
    /**
     * 年度标准保费排行榜（BAPP 相关接口）
     * @param query
     * @return
     */
    @ApiOperation(value = "年度标准保费排行榜（BAPP 相关接口）")
    @PostMapping("/rank/assessConvert/year")
    public PageInfo<AssistantSalerRankDTO> pageEmpAssessConvertRankMonth(@RequestBody AssistantSalerRankQuery query) {

        UserDetailVO userDetail= bmsService.getContextUserDetail();
        CommonResult<DataAuthDTO> commonResult = dataAuthFacade.dataAuth(bmsService.getToken());
        if (userDetail==null || !commonResult.isSuccess()){
            throw new MSBizNormalException(ExcptEnum.PRODUCT_ERROR_201010);
        }
        DataAuthDTO dataAuthDTO = commonResult.getData();

        return assistantSalerService.pageAssessConvertRank(query);
    }
    /**
     * 个人推广费排名（BAPP 相关接口）
     * @param query
     * @return
     */
    @ApiOperation(value = "个人推广费排名")
    @PostMapping("/rank/self/promotion/year")
    public AssistantSalerRankDTO empPromotionRankYear(@RequestBody AssistantSalerRankQuery query) {

        UserDetailVO userDetail= bmsService.getContextUserDetail();
        CommonResult<DataAuthDTO> commonResult = dataAuthFacade.dataAuth(bmsService.getToken());
        if (userDetail==null || !commonResult.isSuccess()){
            throw new MSBizNormalException(ExcptEnum.PRODUCT_ERROR_201010);
        }
        query.setEmployeeCode(userDetail.getJobNumber());
        DataAuthDTO dataAuthDTO = commonResult.getData();

        return new AssistantSalerRankDTO();
        //return assistantSalerService.getAssistantSalerRank(userDetail.getJobNumber(),dataAuthDTO.getOrgCode(),dataAuthDTO.getRegionCode(),pt, DateTypeEnum.month);
    }
    /**
     * 个人标准保费排名（BAPP 相关接口）
     * @param query
     * @return
     */
    @ApiOperation(value = "")
    @PostMapping("/rank/self/assessConvert/year")
    public AssistantSalerRankDTO empAssessConvertRankYear(@RequestBody AssistantSalerRankQuery query) {

        UserDetailVO userDetail= bmsService.getContextUserDetail();
        CommonResult<DataAuthDTO> commonResult = dataAuthFacade.dataAuth(bmsService.getToken());
        if (userDetail==null || !commonResult.isSuccess()){
            throw new MSBizNormalException(ExcptEnum.PRODUCT_ERROR_201010);
        }
        query.setEmployeeCode(userDetail.getJobNumber());
        DataAuthDTO dataAuthDTO = commonResult.getData();

        return new AssistantSalerRankDTO();
        //return assistantSalerService.getAssistantSalerRank(userDetail.getJobNumber(),dataAuthDTO.getOrgCode(),dataAuthDTO.getRegionCode(),pt, DateTypeEnum.month);
    }
    @ApiOperation(value = "清理排名缓存")
    @PostMapping("/rank/cache/clear")
    public CommonResult<String> clearRankCache(@RequestParam(value = "pt", required = false) String pt) {
        try {
            assistantSalerService.clearRankCache(pt);
            String message = org.springframework.util.StringUtils.isEmpty(pt) ? "已清理所有排名缓存" : "已清理日期 " + pt + " 的排名缓存";
            return CommonResult.successResult(message);
        } catch (Exception e) {
            return CommonResult.failResult();
        }
    }
}
