package com.cfpamf.ms.insur.report.service.behavior.interfaces;

import com.cfpamf.ms.insur.report.pojo.po.BehaviorLogTask;

/**
 * 日志拉取服务接口
 *
 * <AUTHOR>
 * @date 2025-09-30
 */
public interface LogPullService {

    /**
     * 执行日志拉取任务
     *
     * @param task 日志拉取任务
     * @return 是否执行成功
     */
    boolean executeTask(BehaviorLogTask task);

    /**
     * 停止指定任务的执行
     *
     * @param taskId 任务ID
     * @return 是否停止成功
     */
    boolean stopTask(Long taskId);

    /**
     * 检查任务是否正在执行
     *
     * @param taskId 任务ID
     * @return 是否正在执行
     */
    boolean isTaskRunning(Long taskId);

    /**
     * 获取任务执行进度
     *
     * @param taskId 任务ID
     * @return 执行进度（0-100）
     */
    int getTaskProgress(Long taskId);
}
