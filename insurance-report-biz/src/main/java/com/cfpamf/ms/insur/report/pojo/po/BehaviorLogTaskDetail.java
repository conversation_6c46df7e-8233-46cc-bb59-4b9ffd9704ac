package com.cfpamf.ms.insur.report.pojo.po;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户行为日志拉取任务明细表
 *
 * <AUTHOR>
 * @date 2025-09-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "behavior_log_task_detail")
public class BehaviorLogTaskDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 任务ID
     */
    @Column(name = "task_id")
    private Long taskId;

    /**
     * 分片ID
     */
    @Column(name = "shard_id")
    private Integer shardId;

    /**
     * 当前游标值
     */
    @Column(name = "cursor_value")
    private String cursorValue;

    /**
     * 下一个游标值
     */
    @Column(name = "next_cursor")
    private String nextCursor;

    /**
     * 已处理日志数量
     */
    @Column(name = "processed_count")
    private Long processedCount;

    /**
     * 最后处理时间
     */
    @Column(name = "last_process_time")
    private LocalDateTime lastProcessTime;

    /**
     * 状态：0-未开始，1-进行中，2-已完成，3-失败
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    /**
     * 错误信息
     */
    @Column(name = "error_message")
    private String errorMessage;

    /**
     * 状态枚举
     */
    public enum DetailStatus {
        NOT_STARTED(0, "未开始"),
        IN_PROGRESS(1, "进行中"),
        COMPLETED(2, "已完成"),
        FAILED(3, "失败");

        private final Integer code;
        private final String description;

        DetailStatus(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static DetailStatus fromCode(Integer code) {
            for (DetailStatus status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }
}
