package com.cfpamf.ms.insur.report.dao.safepg;

import com.cfpamf.ms.insur.report.dao.CommonMapper;
import com.cfpamf.ms.insur.report.pojo.po.UserBehaviorLog;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户行为日志表数据访问层
 *
 * <AUTHOR>
 * @date 2025-09-30
 */
@Mapper
public interface UserBehaviorLogMapper extends CommonMapper<UserBehaviorLog> {

    /**
     * 根据任务ID查询用户行为日志
     *
     * @param taskId 任务ID
     * @return 用户行为日志列表
     */
    @Select("SELECT * FROM user_behavior_log WHERE task_id = #{taskId} ORDER BY event_time DESC")
    List<UserBehaviorLog> findByTaskId(@Param("taskId") Long taskId);

    /**
     * 根据用户ID查询用户行为日志
     *
     * @param userId 用户ID
     * @return 用户行为日志列表
     */
    @Select("SELECT * FROM user_behavior_log WHERE user_id = #{userId} ORDER BY event_time DESC")
    List<UserBehaviorLog> findByUserId(@Param("userId") String userId);

    /**
     * 根据时间范围查询用户行为日志
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 用户行为日志列表
     */
    @Select("SELECT * FROM user_behavior_log WHERE event_time >= #{startTime} AND event_time <= #{endTime} ORDER BY event_time DESC")
    List<UserBehaviorLog> findByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 批量插入用户行为日志
     *
     * @param behaviorLogs 用户行为日志列表
     * @return 插入行数
     */
    @Insert("<script>" +
            "INSERT INTO user_behavior_log (task_id, user_id, session_id, event_type, event_name, page_url, page_title, " +
            "element_id, element_text, event_time, ip_address, user_agent, device_type, browser_type, os_type, " +
            "screen_resolution, referrer_url, duration, custom_attributes, raw_log_data, created_time) VALUES " +
            "<foreach collection='behaviorLogs' item='log' separator=','>" +
            "(#{log.taskId}, #{log.userId}, #{log.sessionId}, #{log.eventType}, #{log.eventName}, #{log.pageUrl}, " +
            "#{log.pageTitle}, #{log.elementId}, #{log.elementText}, #{log.eventTime}, #{log.ipAddress}, " +
            "#{log.userAgent}, #{log.deviceType}, #{log.browserType}, #{log.osType}, #{log.screenResolution}, " +
            "#{log.referrerUrl}, #{log.duration}, #{log.customAttributes}, #{log.rawLogData}, CURRENT_TIMESTAMP)" +
            "</foreach>" +
            "</script>")
    int batchInsert(@Param("behaviorLogs") List<UserBehaviorLog> behaviorLogs);

    /**
     * 根据事件类型统计数量
     *
     * @param eventType 事件类型
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数量
     */
    @Select("SELECT COUNT(*) FROM user_behavior_log WHERE event_type = #{eventType} AND event_time >= #{startTime} AND event_time <= #{endTime}")
    Long countByEventType(@Param("eventType") String eventType, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 根据用户ID和时间范围查询用户行为日志
     *
     * @param userId    用户ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 用户行为日志列表
     */
    @Select("SELECT * FROM user_behavior_log WHERE user_id = #{userId} AND event_time >= #{startTime} AND event_time <= #{endTime} ORDER BY event_time DESC")
    List<UserBehaviorLog> findByUserIdAndTimeRange(@Param("userId") String userId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 删除指定任务的所有日志
     *
     * @param taskId 任务ID
     * @return 删除行数
     */
    @Delete("DELETE FROM user_behavior_log WHERE task_id = #{taskId}")
    int deleteByTaskId(@Param("taskId") Long taskId);

    /**
     * 根据时间范围删除过期日志
     *
     * @param beforeTime 时间点
     * @return 删除行数
     */
    @Delete("DELETE FROM user_behavior_log WHERE created_time < #{beforeTime}")
    int deleteExpiredLogs(@Param("beforeTime") LocalDateTime beforeTime);
}
