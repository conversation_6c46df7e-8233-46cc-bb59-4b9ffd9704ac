package com.cfpamf.ms.insur.report.dao.safepg;

import java.util.List;

import com.cfpamf.ms.insur.report.pojo.dto.DailyRetrospectiveBasicDTO;
import com.cfpamf.ms.insur.report.pojo.po.AdsInsuranceEmpMarketingProgressDfp;
import com.cfpamf.ms.insur.report.pojo.vo.assistant.DiagnosisRankInfoVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

/**
 * 保险租售员工集市;(ads_insurance_emp_marketing_progress_dfp)表数据库访问层
 *
 * <AUTHOR> http://www.chiner.pro
 * @date : 2024-2-26
 */
@Mapper
public interface AdsInsuranceEmpMarketingProgressDfpMapper {
    /**
     * 通过ID查询单条数据
     *
     * @param undefinedId 主键
     * @return 实例对象
     */
    AdsInsuranceEmpMarketingProgressDfp queryById(String empCode, String pt);

    /**
     * 通过查询条件获取分支内排名
     *
     * @param empCode 员工编码
     * @param bchCode 分支编码
     * @return 实例对象
     */
    AdsInsuranceEmpMarketingProgressDfp queryRankBch(String empCode, String bchCode, String pt, String dataType);

    /**
     * 通过查询条件获取当前名次，当前分支的前一名
     *
     * @param bchCode   分支编码
     * @param rankInBch 当前分支内排名
     * @param pt        分区
     * @return 实例对象
     */
    AdsInsuranceEmpMarketingProgressDfp queryRankBchPrevious(String bchCode, Integer rankInBch, String pt, String dataType);

    /**
     * 通过查询条件获取区域内排名
     *
     * @param empCode  员工编码
     * @param areaCode 区域编码
     * @return 实例对象
     */
    AdsInsuranceEmpMarketingProgressDfp queryRankArea(String empCode, String areaCode, String pt, String dataType);

    /**
     * 通过查询条件获取当前名次，当前区域的前一名
     *
     * @param areaCode   区域编码
     * @param rankInArea 当前区域内排名
     * @param pt         分区
     * @return 实例对象
     */
    AdsInsuranceEmpMarketingProgressDfp queryRankAreaPrevious(String areaCode, Integer rankInArea, String pt, String dataType);

    /**
     * 通过查询条件获取全国排名
     *
     * @param empCode 员工编码
     * @return 实例对象
     */
    AdsInsuranceEmpMarketingProgressDfp queryRankCountry(String empCode, String pt, String dataType);

    /**
     * 通过查询条件获取当前名次，全国的前一名
     *
     * @param rankInCountry 当前全国排名
     * @param pt            分区
     * @return 实例对象
     */
    AdsInsuranceEmpMarketingProgressDfp queryRankCountryPrevious(Integer rankInCountry, String pt, String dataType);

    /**
     * 通过查询条件获取业绩趋势
     *
     * @param empCode           员工编码
     * @param ptListCurrentYear 当前年份的PT列表
     * @return 实例对象
     */
    List<AdsInsuranceEmpMarketingProgressDfp> queryEmpAmtTrend(String empCode, List<String> ptListCurrentYear);

    /**
     * 通过查询条件获取保费配比趋势
     *
     * @param empCode           员工编码
     * @param ptListCurrentYear 当前年份的PT列表
     * @return 实例对象
     */
    List<AdsInsuranceEmpMarketingProgressDfp> queryEmpATypeCustInsuranceRateTrend(String empCode, List<String> ptListCurrentYear);

    /**
     * 通过查询条件获取A类客户转化率趋势
     *
     * @param empCode           员工编码
     * @param ptListCurrentYear 当前年份的PT列表
     * @return 实例对象
     */
    List<AdsInsuranceEmpMarketingProgressDfp> queryEmpATypeCustTransRateTrend(String empCode, List<String> ptListCurrentYear);

    /**
     * 通过查询条件获取A类客户转化率趋势
     *
     * @param empCode           员工编码
     * @param ptListCurrentYear 当前年份的PT列表
     * @return 实例对象
     */
    List<AdsInsuranceEmpMarketingProgressDfp> queryEmpRetentionRate(String empCode, List<String> ptListCurrentYear);

    /**
     * 异业客户转化指标-日复盘用
     *
     * @param empCodes 员工编码列表
     * @param bchCode 分支编码
     * @param pt 分区日期
     * @return 实例对象
     */
    List<AdsInsuranceEmpMarketingProgressDfp> getLoanCustomerConvertMetrics(List<String> empCodes,String bchCode,String pt);

    /**
     * 信贷与非贷标保情况-日复盘用
     *
     * @param empCodes 员工编码列表
     * @param bchCode 分支编码
     * @param pt 当日分区日期
     * @param yesterdayPt 前一天的分区日期
     * @param maxPt 当前月最大分区日期
     * @return 实例对象
     */
    List<AdsInsuranceEmpMarketingProgressDfp> getLoanAndUnloanMetrics(List<String> empCodes,String bchCode,String pt,String yesterdayPt,String maxPt);

    /**
     * 客户留存情况-日复盘用
     *
     * @param empCodes 员工编码列表
     * @param bchCode 分支编码
     * @param pt 分区日期
     * @return 实例对象
     */
    List<AdsInsuranceEmpMarketingProgressDfp> getCustomerRetentionMetrics(List<String> empCodes,String bchCode,String pt);

    List<AdsInsuranceEmpMarketingProgressDfp> getTrendInsuranceRateWithBch(String empCode,String bchCode, List<String> pts);

    /**
     * 分页查询指定行数据
     *
     * @param adsInsuranceEmpMarketingProgressDfp 查询条件
     * @param pageable                            分页对象
     * @return 对象列表
     */
    List<AdsInsuranceEmpMarketingProgressDfp> queryAllByLimit(AdsInsuranceEmpMarketingProgressDfp adsInsuranceEmpMarketingProgressDfp, @Param("pageable") Pageable pageable);


    /**
     * 统计总行数
     *
     * @param adsInsuranceEmpMarketingProgressDfp 查询条件
     * @return 总行数
     */
    long count(AdsInsuranceEmpMarketingProgressDfp adsInsuranceEmpMarketingProgressDfp);

    /**
     * 新增数据
     *
     * @param adsInsuranceEmpMarketingProgressDfp 实例对象
     * @return 影响行数
     */
    int insert(AdsInsuranceEmpMarketingProgressDfp adsInsuranceEmpMarketingProgressDfp);

    /**
     * 批量新增数据
     *
     * @param entities List<AdsInsuranceEmpMarketingProgressDfp> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<AdsInsuranceEmpMarketingProgressDfp> entities);

    /**
     * 批量新增或按主键更新数据
     *
     * @param entities List<AdsInsuranceEmpMarketingProgressDfp> 实例对象列表
     * @return 影响行数
     */
    int insertOrUpdateBatch(@Param("entities") List<AdsInsuranceEmpMarketingProgressDfp> entities);

    /**
     * 更新数据
     *
     * @param adsInsuranceEmpMarketingProgressDfp 实例对象
     * @return 影响行数
     */
    int update(AdsInsuranceEmpMarketingProgressDfp adsInsuranceEmpMarketingProgressDfp);

    /**
     * 通过主键删除数据
     *
     * @param undefinedId 主键
     * @return 影响行数
     */
    int deleteById(String undefinedId);

    /**
     * 查询排名信息
     *
     * @param pt                           分区
     * @param assessConvertInsuranceAmtTag 标准保费是否达标标识
     * @param offlineLoanInsuranceRateTag  异业保费配比是否达标标识
     * @param insuranceRetentionRateTag
     * @param orgCodes                     分支编码
     * @return 排名信息
     */
    List<DiagnosisRankInfoVo> queryRankInfo(String pt, boolean assessConvertInsuranceAmtTag, boolean offlineLoanInsuranceRateTag, boolean insuranceRetentionRateTag, List<String> orgCodes);

    /**
     * 查询业务小助手中诊断相关的数据
     *
     * @param pt                           分区
     * @param assessConvertInsuranceAmtTag 标准保费达成率标识
     * @param orgCodes                     组织机构代码
     * @return 诊断数据
     */
    List<DiagnosisRankInfoVo> queryAssessConvertInsuranceAmtSummaryRankInfo(String pt, Boolean assessConvertInsuranceAmtTag, List<String> orgCodes);

    /**
     * 查询管理助手小结-异业保费配比小结的排名信息
     *
     * @param pt                     分区
     * @param insuranceAmountRateTag 异业保费配比是否达标
     * @param orgCodes               分支编码
     * @return 排名信息
     */
    List<DiagnosisRankInfoVo> queryInsuranceAmountRateSummaryRankInfo(String pt, boolean insuranceAmountRateTag, List<String> orgCodes);

    /**
     * 查询客户留存率小结的排名信息
     *
     * @param pt               分区
     * @param retentionRateTag 客户留存率是否达标的标识
     * @param orgCodes         分支编码
     * @return 排名信息
     */
    List<DiagnosisRankInfoVo> queryRetentionRateSummaryRankInfo(String pt, boolean retentionRateTag, List<String> orgCodes);
    /**
     * 查询客户业绩达成指标
     *
     * @param empCodes               员工工号列表
     * @param bchCode 分支
     * @param pt         分区
     * @return AdsInsuranceEmpMarketingProgressDfp
     */
    List<AdsInsuranceEmpMarketingProgressDfp> getCustomerConvertInsuranceMetrics(List<String> empCodes, String bchCode,String pt,String date,String monday);

    /**
     *
     * @param pt
     * @param dataType
     * @return
     */
    List<AdsInsuranceEmpMarketingProgressDfp> listAssessConvertRankCountry(String pt, String dataType);

    /**
     *
     * @param pt
     * @param dataType
     * @return
     */
    List<AdsInsuranceEmpMarketingProgressDfp> listPromotionRankCountry(String pt, String dataType);
}
