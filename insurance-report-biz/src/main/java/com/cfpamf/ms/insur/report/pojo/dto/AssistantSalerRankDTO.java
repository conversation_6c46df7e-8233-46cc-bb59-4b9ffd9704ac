package com.cfpamf.ms.insur.report.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Data
@ApiModel(value = "保险组手员工排名", description = "")
public class AssistantSalerRankDTO {
    @ApiModelProperty(value = "员工姓名", notes = "")
    private String empName;
    /**
     * 工号
     */
    @ApiModelProperty(value = "工号", notes = "")
    private String empCode;
    /**
     * 分支
     */
    @ApiModelProperty(value = "分支", notes = "")
    private String bchName;
    /**
     * 分支编码
     */
    @ApiModelProperty(value = "分支编码", notes = "")
    private String bchCode;
    /**
     * 片区
     */
    @ApiModelProperty(value = "片区", notes = "")
    private String districtName;
    /**
     * 片区编码
     */
    @ApiModelProperty(value = "片区编码", notes = "")
    private String districtCode;
    /**
     * 区域
     */
    @ApiModelProperty(value = "区域", notes = "")
    private String areaName;
    /**
     * 区域编码
     */
    @ApiModelProperty(value = "区域编码", notes = "")
    private String areaCode;
    /**
     * 排名
     */
    private Integer rank;
    /**
     * 费用
     */
    private Double amt;

    /**
     * 昨日费用
     */
    private Double yesterdayAmt;

//    /**
//     * 当月标准保费
//     */
//    @ApiModelProperty(value = "当月标准保费", notes = "")
//    private Double smAssessConvertInsuranceAmt;
//    /**
//     * 当年标准保费
//     */
//    @ApiModelProperty(value = "当年标准保费", notes = "")
//    private Double syAssessConvertInsuranceAmt;
//
//    /**
//     * 当月标准保费
//     */
//    @ApiModelProperty(value = "当年推广费", notes = "")
//    private Double smPromotionFeeAmt;
//    /**
//     * 当年推广费
//     */
//    @ApiModelProperty(value = "当年推广费", notes = "")
//    private Double syPromotionFeeAmt;
//
//
//    public Double getSmPromotionAmt() {
//        //判断是否为空，是则返回0.00
//        if(smPromotionFeeAmt==null){
//            return 0.00;
//        }
//        //格式化为两位小数
//        smPromotionFeeAmt = BigDecimal.valueOf(smPromotionFeeAmt).setScale(4, RoundingMode.HALF_UP).doubleValue();
//        return smPromotionFeeAmt;
//    }
//
//    public Double getSmAssessConvertInsuranceAmt() {
//        //判断是否为空，是则返回0.00
//        if(smAssessConvertInsuranceAmt==null){
//            return 0.00;
//        }
//        //格式化为两位小数
//        smAssessConvertInsuranceAmt = BigDecimal.valueOf(smAssessConvertInsuranceAmt).setScale(4, RoundingMode.HALF_UP).doubleValue();
//        return smAssessConvertInsuranceAmt;
//    }
//
//    public Double getSyAssessConvertInsuranceAmt() {
//        //判断是否为空，是则返回0.00
//        if(syAssessConvertInsuranceAmt==null){
//            return 0.00;
//        }
//        //格式化为两位小数
//        syAssessConvertInsuranceAmt = BigDecimal.valueOf(syAssessConvertInsuranceAmt).setScale(4, RoundingMode.HALF_UP).doubleValue();
//        return syAssessConvertInsuranceAmt;
//    }
//
//    public Double getSyPromotionAmt() {
//        //判断是否为空，是则返回0.00
//        if(syPromotionFeeAmt==null){
//            return 0.00;
//        }
//        //格式化为两位小数
//        syPromotionFeeAmt = BigDecimal.valueOf(syPromotionFeeAmt).setScale(4, RoundingMode.HALF_UP).doubleValue();
//        return syPromotionFeeAmt;
//    }
}
