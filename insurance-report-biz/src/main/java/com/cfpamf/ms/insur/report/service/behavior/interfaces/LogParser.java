package com.cfpamf.ms.insur.report.service.behavior.interfaces;

import com.aliyun.openservices.log.common.FastLogGroup;
import com.cfpamf.ms.insur.report.pojo.po.UserBehaviorLog;

import java.util.List;

/**
 * 日志解析器接口
 *
 * <AUTHOR>
 * @date 2025-09-30
 */
public interface LogParser {

    /**
     * 解析FastLogGroup为用户行为日志列表
     *
     * @param taskId       任务ID
     * @param fastLogGroup FastLogGroup对象
     * @return 用户行为日志列表
     */
    List<UserBehaviorLog> parseLogGroup(Long taskId, FastLogGroup fastLogGroup);

    /**
     * 解析单条日志内容
     *
     * @param taskId     任务ID
     * @param logContent 日志内容
     * @return 用户行为日志
     */
    UserBehaviorLog parseLogContent(Long taskId, String logContent);

    /**
     * 验证日志格式是否正确
     *
     * @param logContent 日志内容
     * @return 是否有效
     */
    boolean isValidLogFormat(String logContent);
}
