package com.cfpamf.ms.insur.report.service.behavior.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 阿里云SLS配置类
 *
 * <AUTHOR>
 * @date 2025-09-30
 */
@Data
@Component
@ConfigurationProperties(prefix = "aliyun.sls")
public class SlsConfig {

    /**
     * SLS服务端点
     */
    private String endpoint;

    /**
     * 访问密钥ID
     */
    private String accessKeyId;

    /**
     * 访问密钥Secret
     */
    private String accessKeySecret;

    /**
     * 安全令牌（可选）
     */
    private String securityToken;

    /**
     * 连接超时时间（毫秒）
     */
    private Integer connectionTimeout = 30000;

    /**
     * 读取超时时间（毫秒）
     */
    private Integer readTimeout = 60000;

    /**
     * 每批次拉取的最大日志数量
     */
    private Integer batchSize = 1000;

    /**
     * 每次处理的最大日志数量（防止内存溢出）
     */
    private Integer maxProcessSize = 10000;

    /**
     * 游标获取超时时间（秒）
     */
    private Integer cursorTimeout = 60;

    /**
     * 日志拉取间隔时间（毫秒）
     */
    private Long pullInterval = 5000L;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount = 3;

    /**
     * 重试间隔时间（毫秒）
     */
    private Long retryInterval = 1000L;

    /**
     * 是否启用日志拉取功能
     */
    private Boolean enabled = true;

    /**
     * 线程池核心线程数
     */
    private Integer corePoolSize = 5;

    /**
     * 线程池最大线程数
     */
    private Integer maxPoolSize = 10;

    /**
     * 线程池队列容量
     */
    private Integer queueCapacity = 100;

    /**
     * 线程池线程名前缀
     */
    private String threadNamePrefix = "sls-pull-";
}
