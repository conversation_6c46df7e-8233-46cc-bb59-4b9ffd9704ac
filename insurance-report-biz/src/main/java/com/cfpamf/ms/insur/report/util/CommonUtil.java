package com.cfpamf.ms.insur.report.util;

import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.report.constant.BaseConstants;
import com.cfpamf.ms.insur.report.enums.DateTypeEnum;
import com.cfpamf.ms.insur.report.pojo.query.PromoFeeQuery;
import com.cfpamf.ms.insur.report.pojo.query.RenewalRateQuery;
import com.cfpamf.ms.insur.report.pojo.query.ReportQuery;
import org.apache.commons.lang.StringUtils;

import java.time.LocalDate;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class CommonUtil {
    /**
     * @param dateType
     * @param dateStr
     * @return
     */
    public static String dateStrChange(String dateType, String dateStr) {
        if (Objects.equals(dateType, DateTypeEnum.day.getCode())) {
            return dateStr;
        } else if (Objects.equals(dateType, DateTypeEnum.month.getCode())) {
            return dateStr + "-01";
        } else if (Objects.equals(dateType, DateTypeEnum.year.getCode())) {
            return dateStr + "-01-01";
        } else {
            return dateStr;
        }
    }

    /**
     * 验证业绩报表日期
     *
     * @param query
     */
    public static void validateQueryDate(ReportQuery query) {
        if (StringUtils.isBlank(query.getEndDateStr())) {
            query.setEndDate(LocalDate.now().plusDays(-1));
        } else {
            //query.setEndDate(LocalDate.parse(dateStrChange(query.getDateType(),query.getEndDateStr())));
            LocalDate endDate = LocalDateUtil.getMonthEndDate(LocalDate.parse(dateStrChange(query.getDateType(), query.getEndDateStr())));
            if (endDate.isAfter(LocalDate.now())) {
                query.setEndDate(LocalDate.now().plusDays(-1));
            } else {
                query.setEndDate(endDate);
            }
        }
        if (StringUtils.isBlank(query.getStartDateStr())) {
            query.setStartDate(LocalDateUtil.getMonthStartDate(query.getEndDate()));
        } else {
            query.setStartDate(LocalDate.parse(dateStrChange(query.getDateType(), query.getStartDateStr())));
        }

    }

    /**
     * 验证续保率报表日期
     *
     * @param query
     */
    public static void validateRenewalRateDate(RenewalRateQuery query) {
        if (StringUtils.isBlank(query.getEndDateStr())) {
            query.setEndDate(LocalDate.now().plusDays(-1));
        } else {
            LocalDate endDate = LocalDateUtil.getMonthEndDate(LocalDate.parse(dateStrChange(query.getDateType(), query.getEndDateStr())));
            if (endDate.isAfter(LocalDate.now())) {
                query.setEndDate(LocalDate.now().plusDays(-1));
            } else {
                query.setEndDate(endDate);
            }

        }
        if (Objects.equals(query.getRecordType(), "year")) {
            query.setStartDate(LocalDateUtil.getYearStartDate(query.getEndDate()));
        } else {
            query.setStartDate(LocalDateUtil.getMonthStartDate(query.getEndDate()));
        }
    }

    /**
     * 验证推广费查询日期
     *
     * @param query
     */
    public static void validatePromoFeeDate(PromoFeeQuery query) {
        validatePromoFeeDate(query, LocalDateUtil.getPreMonthEndDate());
    }

    /**
     * 验证推广费查询日期
     *
     * @param query          查询条件
     * @param defaultEndDate 当endDate为null或者超过当前时间后的缺省值
     */
    public static void validatePromoFeeDate(PromoFeeQuery query, LocalDate defaultEndDate) {
        if (StringUtils.isBlank(query.getStartDateStr())) {
            //如果开始时间为空设置开始时间为"2021-01-01"
            query.setStartDate(LocalDateUtil.getMonthStartDate(LocalDate.parse(BaseConstants.REPORT_DEFAULT_START_DATE)));
        } else {
            //设置开始时间为月初
            query.setStartDate(LocalDateUtil.getMonthStartDate(LocalDate.parse(dateStrChange(query.getDateType(), query.getStartDateStr()))));
        }

        if (StringUtils.isBlank(query.getEndDateStr())) {
            //如果结束日期为空
            if (defaultEndDate.isAfter(LocalDate.parse(BaseConstants.REPORT_DEFAULT_START_DATE))) {
                //设置结束日期为自定义缺省值
                query.setEndDate(defaultEndDate);
            } else {
                //设置结束日期为"2021-01-31"
                query.setEndDate(LocalDateUtil.getMonthEndDate(LocalDate.parse(BaseConstants.REPORT_DEFAULT_START_DATE)));
            }
        } else {
            //获取结束日期的月末日期
            LocalDate endDate = LocalDateUtil.getMonthEndDate(LocalDate.parse(CommonUtil.dateStrChange(query.getDateType(), query.getEndDateStr())));
            if (endDate.isAfter(LocalDate.now())) {
                query.setEndDate(defaultEndDate);
            } else {
                query.setEndDate(endDate);
            }
        }
    }

    public static void main(String[] args){
        Calendar c = Calendar.getInstance();
        System.out.println(DateUtil.format(c.getTime(),"yyyy年MM月dd日"));
        c.add(Calendar.DAY_OF_MONTH,-1);
        String bizTime = DateUtil.format(c.getTime(),"yyyyMMdd");

        System.out.println(DateUtil.format(c.getTime(),"MM月"));
    }

}
