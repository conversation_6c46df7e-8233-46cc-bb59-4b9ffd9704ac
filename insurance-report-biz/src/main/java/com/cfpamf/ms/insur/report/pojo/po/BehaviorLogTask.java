package com.cfpamf.ms.insur.report.pojo.po;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户行为日志拉取任务表
 *
 * <AUTHOR>
 * @date 2025-09-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "behavior_log_task")
public class BehaviorLogTask implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 任务名称
     */
    @Column(name = "task_name")
    private String taskName;

    /**
     * SLS项目名称
     */
    @Column(name = "project_name")
    private String projectName;

    /**
     * SLS日志库名称
     */
    @Column(name = "logstore_name")
    private String logstoreName;

    /**
     * 日志获取开始时间
     */
    @Column(name = "start_time")
    private LocalDateTime startTime;

    /**
     * 日志获取结束时间
     */
    @Column(name = "end_time")
    private LocalDateTime endTime;

    /**
     * 查询条件
     */
    @Column(name = "query_condition")
    private String queryCondition;

    /**
     * 任务状态：0-未开始，1-进行中，2-已完成，3-失败
     */
    @Column(name = "task_status")
    private Integer taskStatus;

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    /**
     * 创建人
     */
    @Column(name = "created_by")
    private String createdBy;

    /**
     * 更新人
     */
    @Column(name = "updated_by")
    private String updatedBy;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        NOT_STARTED(0, "未开始"),
        IN_PROGRESS(1, "进行中"),
        COMPLETED(2, "已完成"),
        FAILED(3, "失败");

        private final Integer code;
        private final String description;

        TaskStatus(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static TaskStatus fromCode(Integer code) {
            for (TaskStatus status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }
}
