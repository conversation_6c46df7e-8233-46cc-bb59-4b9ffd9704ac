package com.cfpamf.ms.insur.report.service.behavior.factory;

import com.aliyun.openservices.log.Client;
import com.cfpamf.ms.insur.report.service.behavior.config.SlsConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.concurrent.ConcurrentHashMap;

/**
 * SLS客户端工厂类
 *
 * <AUTHOR>
 * @date 2025-09-30
 */
@Slf4j
@Component
public class SlsClientFactory {

    @Autowired
    private SlsConfig slsConfig;

    /**
     * 客户端缓存
     */
    private final ConcurrentHashMap<String, Client> clientCache = new ConcurrentHashMap<>();

    /**
     * 获取SLS客户端
     *
     * @return SLS客户端
     */
    public Client getClient() {
        return getClient(slsConfig.getEndpoint());
    }

    /**
     * 根据端点获取SLS客户端
     *
     * @param endpoint 端点
     * @return SLS客户端
     */
    public Client getClient(String endpoint) {
        return clientCache.computeIfAbsent(endpoint, this::createClient);
    }

    /**
     * 创建SLS客户端
     *
     * @param endpoint 端点
     * @return SLS客户端
     */
    private Client createClient(String endpoint) {
        try {
            log.info("创建SLS客户端，端点：{}", endpoint);
            
            Client client;
            if (slsConfig.getSecurityToken() != null && !slsConfig.getSecurityToken().isEmpty()) {
                // 使用STS临时凭证
                client = new Client(endpoint, slsConfig.getAccessKeyId(), 
                                  slsConfig.getAccessKeySecret(), slsConfig.getSecurityToken());
            } else {
                // 使用长期凭证
                client = new Client(endpoint, slsConfig.getAccessKeyId(), slsConfig.getAccessKeySecret());
            }
            
            // 设置超时时间
            client.setConnectionTimeout(slsConfig.getConnectionTimeout());
            client.setReadTimeout(slsConfig.getReadTimeout());
            
            log.info("SLS客户端创建成功，端点：{}", endpoint);
            return client;
        } catch (Exception e) {
            log.error("创建SLS客户端失败，端点：{}", endpoint, e);
            throw new RuntimeException("创建SLS客户端失败", e);
        }
    }

    /**
     * 关闭指定端点的客户端
     *
     * @param endpoint 端点
     */
    public void closeClient(String endpoint) {
        Client client = clientCache.remove(endpoint);
        if (client != null) {
            try {
                client.shutdown();
                log.info("SLS客户端已关闭，端点：{}", endpoint);
            } catch (Exception e) {
                log.warn("关闭SLS客户端失败，端点：{}", endpoint, e);
            }
        }
    }

    /**
     * 关闭所有客户端
     */
    @PreDestroy
    public void closeAllClients() {
        log.info("开始关闭所有SLS客户端");
        for (String endpoint : clientCache.keySet()) {
            closeClient(endpoint);
        }
        log.info("所有SLS客户端已关闭");
    }

    /**
     * 初始化方法
     */
    @PostConstruct
    public void init() {
        log.info("SLS客户端工厂初始化完成");
    }

    /**
     * 获取客户端缓存大小
     *
     * @return 缓存大小
     */
    public int getCacheSize() {
        return clientCache.size();
    }

    /**
     * 清空客户端缓存
     */
    public void clearCache() {
        closeAllClients();
        clientCache.clear();
        log.info("SLS客户端缓存已清空");
    }
}
