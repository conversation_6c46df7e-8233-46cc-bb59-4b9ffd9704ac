package com.cfpamf.ms.insur.report.dao.safepg;

import com.cfpamf.ms.insur.report.dao.CommonMapper;
import com.cfpamf.ms.insur.report.pojo.po.BehaviorLogTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 用户行为日志拉取任务表数据访问层
 *
 * <AUTHOR>
 * @date 2025-09-30
 */
@Mapper
public interface BehaviorLogTaskMapper extends CommonMapper<BehaviorLogTask> {

    /**
     * 根据任务状态查询任务列表
     *
     * @param taskStatus 任务状态
     * @return 任务列表
     */
    @Select("SELECT * FROM behavior_log_task WHERE task_status = #{taskStatus} ORDER BY created_time ASC")
    List<BehaviorLogTask> findByTaskStatus(@Param("taskStatus") Integer taskStatus);

    /**
     * 根据项目名称查询任务列表
     *
     * @param projectName 项目名称
     * @return 任务列表
     */
    @Select("SELECT * FROM behavior_log_task WHERE project_name = #{projectName} ORDER BY created_time DESC")
    List<BehaviorLogTask> findByProjectName(@Param("projectName") String projectName);

    /**
     * 更新任务状态
     *
     * @param id         任务ID
     * @param taskStatus 任务状态
     * @param updatedBy  更新人
     * @return 更新行数
     */
    @Update("UPDATE behavior_log_task SET task_status = #{taskStatus}, updated_by = #{updatedBy}, updated_time = CURRENT_TIMESTAMP WHERE id = #{id}")
    int updateTaskStatus(@Param("id") Long id, @Param("taskStatus") Integer taskStatus, @Param("updatedBy") String updatedBy);

    /**
     * 查询需要执行的任务（状态为未开始或进行中）
     *
     * @return 任务列表
     */
    @Select("SELECT * FROM behavior_log_task WHERE task_status IN (0, 1) ORDER BY created_time ASC")
    List<BehaviorLogTask> findExecutableTasks();

    /**
     * 根据项目名称和日志库名称查询任务
     *
     * @param projectName  项目名称
     * @param logstoreName 日志库名称
     * @return 任务列表
     */
    @Select("SELECT * FROM behavior_log_task WHERE project_name = #{projectName} AND logstore_name = #{logstoreName} ORDER BY created_time DESC")
    List<BehaviorLogTask> findByProjectAndLogstore(@Param("projectName") String projectName, @Param("logstoreName") String logstoreName);
}
