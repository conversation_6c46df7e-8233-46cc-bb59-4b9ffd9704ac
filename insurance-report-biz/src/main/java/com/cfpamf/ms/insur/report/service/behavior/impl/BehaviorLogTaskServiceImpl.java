package com.cfpamf.ms.insur.report.service.behavior.impl;

import com.cfpamf.ms.insur.report.dao.safepg.BehaviorLogTaskDetailMapper;
import com.cfpamf.ms.insur.report.dao.safepg.BehaviorLogTaskMapper;
import com.cfpamf.ms.insur.report.dao.safepg.UserBehaviorLogMapper;
import com.cfpamf.ms.insur.report.pojo.po.BehaviorLogTask;
import com.cfpamf.ms.insur.report.pojo.po.BehaviorLogTaskDetail;
import com.cfpamf.ms.insur.report.service.behavior.interfaces.BehaviorLogTaskService;
import com.cfpamf.ms.insur.report.service.behavior.interfaces.LogPullService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户行为日志任务服务实现类
 *
 * <AUTHOR>
 * @date 2025-09-30
 */
@Slf4j
@Service
public class BehaviorLogTaskServiceImpl implements BehaviorLogTaskService {

    @Autowired
    private BehaviorLogTaskMapper taskMapper;

    @Autowired
    private BehaviorLogTaskDetailMapper taskDetailMapper;

    @Autowired
    private UserBehaviorLogMapper behaviorLogMapper;

    @Autowired
    private LogPullService logPullService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BehaviorLogTask createTask(BehaviorLogTask task) {
        try {
            // 设置默认值
            if (task.getTaskStatus() == null) {
                task.setTaskStatus(BehaviorLogTask.TaskStatus.NOT_STARTED.getCode());
            }
            if (task.getCreatedTime() == null) {
                task.setCreatedTime(LocalDateTime.now());
            }
            if (task.getUpdatedTime() == null) {
                task.setUpdatedTime(LocalDateTime.now());
            }

            // 插入任务
            taskMapper.insertSelective(task);
            
            log.info("创建日志拉取任务成功，taskId: {}, taskName: {}", task.getId(), task.getTaskName());
            return task;
        } catch (Exception e) {
            log.error("创建日志拉取任务失败，taskName: {}", task.getTaskName(), e);
            throw new RuntimeException("创建日志拉取任务失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BehaviorLogTask updateTask(BehaviorLogTask task) {
        try {
            task.setUpdatedTime(LocalDateTime.now());
            taskMapper.updateByPrimaryKeySelective(task);
            
            log.info("更新日志拉取任务成功，taskId: {}", task.getId());
            return task;
        } catch (Exception e) {
            log.error("更新日志拉取任务失败，taskId: {}", task.getId(), e);
            throw new RuntimeException("更新日志拉取任务失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTask(Long taskId) {
        try {
            // 检查任务是否正在执行
            if (isTaskRunning(taskId)) {
                log.warn("任务正在执行中，无法删除，taskId: {}", taskId);
                return false;
            }

            // 删除相关的用户行为日志
            behaviorLogMapper.deleteByTaskId(taskId);
            
            // 删除任务明细
            taskDetailMapper.deleteByTaskId(taskId);
            
            // 删除任务
            taskMapper.deleteByPrimaryKey(taskId);
            
            log.info("删除日志拉取任务成功，taskId: {}", taskId);
            return true;
        } catch (Exception e) {
            log.error("删除日志拉取任务失败，taskId: {}", taskId, e);
            return false;
        }
    }

    @Override
    public BehaviorLogTask getTaskById(Long taskId) {
        try {
            return taskMapper.selectByPrimaryKey(taskId);
        } catch (Exception e) {
            log.error("查询日志拉取任务失败，taskId: {}", taskId, e);
            return null;
        }
    }

    @Override
    public List<BehaviorLogTask> getAllTasks() {
        try {
            return taskMapper.selectAll();
        } catch (Exception e) {
            log.error("查询所有日志拉取任务失败", e);
            return null;
        }
    }

    @Override
    public List<BehaviorLogTask> getTasksByStatus(Integer taskStatus) {
        try {
            return taskMapper.findByTaskStatus(taskStatus);
        } catch (Exception e) {
            log.error("根据状态查询日志拉取任务失败，taskStatus: {}", taskStatus, e);
            return null;
        }
    }

    @Override
    public List<BehaviorLogTask> getExecutableTasks() {
        try {
            return taskMapper.findExecutableTasks();
        } catch (Exception e) {
            log.error("查询可执行的日志拉取任务失败", e);
            return null;
        }
    }

    @Override
    public boolean startTask(Long taskId) {
        try {
            BehaviorLogTask task = getTaskById(taskId);
            if (task == null) {
                log.warn("任务不存在，taskId: {}", taskId);
                return false;
            }

            // 检查任务是否已在执行
            if (isTaskRunning(taskId)) {
                log.warn("任务已在执行中，taskId: {}", taskId);
                return false;
            }

            // 异步执行任务
            new Thread(() -> {
                try {
                    logPullService.executeTask(task);
                } catch (Exception e) {
                    log.error("执行日志拉取任务异常，taskId: {}", taskId, e);
                }
            }).start();

            log.info("启动日志拉取任务成功，taskId: {}", taskId);
            return true;
        } catch (Exception e) {
            log.error("启动日志拉取任务失败，taskId: {}", taskId, e);
            return false;
        }
    }

    @Override
    public boolean stopTask(Long taskId) {
        try {
            boolean success = logPullService.stopTask(taskId);
            if (success) {
                // 更新任务状态
                taskMapper.updateTaskStatus(taskId, BehaviorLogTask.TaskStatus.NOT_STARTED.getCode(), "system");
                log.info("停止日志拉取任务成功，taskId: {}", taskId);
            }
            return success;
        } catch (Exception e) {
            log.error("停止日志拉取任务失败，taskId: {}", taskId, e);
            return false;
        }
    }

    @Override
    public boolean restartTask(Long taskId) {
        try {
            // 先停止任务
            stopTask(taskId);
            
            // 等待一段时间确保任务完全停止
            Thread.sleep(2000);
            
            // 重新启动任务
            return startTask(taskId);
        } catch (Exception e) {
            log.error("重启日志拉取任务失败，taskId: {}", taskId, e);
            return false;
        }
    }

    @Override
    public List<BehaviorLogTaskDetail> getTaskDetails(Long taskId) {
        try {
            return taskDetailMapper.findByTaskId(taskId);
        } catch (Exception e) {
            log.error("查询任务明细失败，taskId: {}", taskId, e);
            return null;
        }
    }

    @Override
    public int getTaskProgress(Long taskId) {
        return logPullService.getTaskProgress(taskId);
    }

    @Override
    public boolean isTaskRunning(Long taskId) {
        return logPullService.isTaskRunning(taskId);
    }
}
