package com.cfpamf.ms.insur.report.constant;

import java.time.format.DateTimeFormatter;

public interface BaseConstants {

    /**
     * 保险整合BMS api不需要前缀
     * 后端管理接口前缀
     */
    String ADMIN_VERSION = "/back";
    /**
     * 微信接口前缀
     */
    String WX_VERSION = "/wx";

    /**
     * 小额保险按区域统计报表文件名
     */
    String SM_TEMPLATE_FILE_PATH = "template";
    /**
     * authorization
     */
    String API_AUTH_NAME = "authorization";

    /**
     * authorization
     */
    String WX_HTTP_HEAD_AUTH = "authorization";

    /**
     * openId
     */
    String WX_HTTP_HEAD_OPENID = "openId";

    /**
     * 根机构hrOrgId
     */
    int BMS_ROOT_ROOT_HR_ORG_ID = 900105153;
    /**
     * 总部职能部门根path
     */
    String BMS_ROOT_HEAD_HR_ORG_ID_PATH = "900105153/282717";
    /**
     * 总部职能部门根节点hrOrgId
     */
    int BMS_ROOT_HEAD_HR_ORG_ID = 282717;
    /**
     * 总部职能部门根节点文本
     */
    String BMS_ROOT_HEAD_HR_ORG_TEXT = "总部";


    /**
     * 用户种类-微信
     */
    String USER_TYPE_WEIXIN = "weixin";
    /**
     * 用户种类-员工
     */
    String USER_TYPE_EMPLOYEE = "employee";
    /**
     * 用户种类-代理人
     */
    String USER_TYPE_AGENT = "agent";

    String REPORT_DEFAULT_START_DATE="2021-01-01";

    DateTimeFormatter FMT_YYYYMMDD = DateTimeFormatter.ofPattern("yyyyMMdd");;
    DateTimeFormatter FMT_YYYY_MM_DD = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    long VALIDATION_BEFORE_DAY = 8L;


}
