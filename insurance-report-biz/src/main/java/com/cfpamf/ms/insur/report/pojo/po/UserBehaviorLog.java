package com.cfpamf.ms.insur.report.pojo.po;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户行为日志表
 *
 * <AUTHOR>
 * @date 2025-09-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "user_behavior_log")
public class UserBehaviorLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 任务ID
     */
    @Column(name = "task_id")
    private Long taskId;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private String userId;

    /**
     * 会话ID
     */
    @Column(name = "session_id")
    private String sessionId;

    /**
     * 事件类型
     */
    @Column(name = "event_type")
    private String eventType;

    /**
     * 事件名称
     */
    @Column(name = "event_name")
    private String eventName;

    /**
     * 页面URL
     */
    @Column(name = "page_url")
    private String pageUrl;

    /**
     * 页面标题
     */
    @Column(name = "page_title")
    private String pageTitle;

    /**
     * 元素ID
     */
    @Column(name = "element_id")
    private String elementId;

    /**
     * 元素文本
     */
    @Column(name = "element_text")
    private String elementText;

    /**
     * 事件时间
     */
    @Column(name = "event_time")
    private LocalDateTime eventTime;

    /**
     * IP地址
     */
    @Column(name = "ip_address")
    private String ipAddress;

    /**
     * 用户代理
     */
    @Column(name = "user_agent")
    private String userAgent;

    /**
     * 设备类型
     */
    @Column(name = "device_type")
    private String deviceType;

    /**
     * 浏览器类型
     */
    @Column(name = "browser_type")
    private String browserType;

    /**
     * 操作系统类型
     */
    @Column(name = "os_type")
    private String osType;

    /**
     * 屏幕分辨率
     */
    @Column(name = "screen_resolution")
    private String screenResolution;

    /**
     * 来源URL
     */
    @Column(name = "referrer_url")
    private String referrerUrl;

    /**
     * 持续时间(毫秒)
     */
    @Column(name = "duration")
    private Long duration;

    /**
     * 自定义属性(JSON格式)
     */
    @Column(name = "custom_attributes")
    private String customAttributes;

    /**
     * 原始日志数据
     */
    @Column(name = "raw_log_data")
    private String rawLogData;

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    private LocalDateTime createdTime;
}
