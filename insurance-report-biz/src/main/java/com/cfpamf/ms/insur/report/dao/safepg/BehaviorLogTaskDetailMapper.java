package com.cfpamf.ms.insur.report.dao.safepg;

import com.cfpamf.ms.insur.report.dao.CommonMapper;
import com.cfpamf.ms.insur.report.pojo.po.BehaviorLogTaskDetail;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户行为日志拉取任务明细表数据访问层
 *
 * <AUTHOR>
 * @date 2025-09-30
 */
@Mapper
public interface BehaviorLogTaskDetailMapper extends CommonMapper<BehaviorLogTaskDetail> {

    /**
     * 根据任务ID查询任务明细列表
     *
     * @param taskId 任务ID
     * @return 任务明细列表
     */
    @Select("SELECT * FROM behavior_log_task_detail WHERE task_id = #{taskId} ORDER BY shard_id ASC")
    List<BehaviorLogTaskDetail> findByTaskId(@Param("taskId") Long taskId);

    /**
     * 根据任务ID和分片ID查询任务明细
     *
     * @param taskId  任务ID
     * @param shardId 分片ID
     * @return 任务明细
     */
    @Select("SELECT * FROM behavior_log_task_detail WHERE task_id = #{taskId} AND shard_id = #{shardId}")
    BehaviorLogTaskDetail findByTaskIdAndShardId(@Param("taskId") Long taskId, @Param("shardId") Integer shardId);

    /**
     * 更新游标值
     *
     * @param id          明细ID
     * @param cursorValue 当前游标值
     * @param nextCursor  下一个游标值
     * @return 更新行数
     */
    @Update("UPDATE behavior_log_task_detail SET cursor_value = #{cursorValue}, next_cursor = #{nextCursor}, updated_time = CURRENT_TIMESTAMP WHERE id = #{id}")
    int updateCursor(@Param("id") Long id, @Param("cursorValue") String cursorValue, @Param("nextCursor") String nextCursor);

    /**
     * 更新处理状态和处理数量
     *
     * @param id             明细ID
     * @param status         状态
     * @param processedCount 已处理数量
     * @param errorMessage   错误信息
     * @return 更新行数
     */
    @Update("UPDATE behavior_log_task_detail SET status = #{status}, processed_count = #{processedCount}, " +
            "last_process_time = CURRENT_TIMESTAMP, updated_time = CURRENT_TIMESTAMP, error_message = #{errorMessage} WHERE id = #{id}")
    int updateProcessStatus(@Param("id") Long id, @Param("status") Integer status, 
                           @Param("processedCount") Long processedCount, @Param("errorMessage") String errorMessage);

    /**
     * 批量插入任务明细
     *
     * @param taskDetails 任务明细列表
     * @return 插入行数
     */
    @Insert("<script>" +
            "INSERT INTO behavior_log_task_detail (task_id, shard_id, status, created_time, updated_time) VALUES " +
            "<foreach collection='taskDetails' item='detail' separator=','>" +
            "(#{detail.taskId}, #{detail.shardId}, #{detail.status}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)" +
            "</foreach>" +
            "</script>")
    int batchInsert(@Param("taskDetails") List<BehaviorLogTaskDetail> taskDetails);

    /**
     * 根据任务ID和状态查询任务明细列表
     *
     * @param taskId 任务ID
     * @param status 状态
     * @return 任务明细列表
     */
    @Select("SELECT * FROM behavior_log_task_detail WHERE task_id = #{taskId} AND status = #{status} ORDER BY shard_id ASC")
    List<BehaviorLogTaskDetail> findByTaskIdAndStatus(@Param("taskId") Long taskId, @Param("status") Integer status);

    /**
     * 删除任务相关的所有明细
     *
     * @param taskId 任务ID
     * @return 删除行数
     */
    @Delete("DELETE FROM behavior_log_task_detail WHERE task_id = #{taskId}")
    int deleteByTaskId(@Param("taskId") Long taskId);
}
