2025-07-07 09:25:16.280 [] INFO [main] com.alibaba.boot.nacos.config.util.NacosConfigPropertiesUtils[47] nacosConfigProperties : NacosConfigProperties{serverAddr='http://mse-b81bd222-nacos-ans.mse.aliyuncs.com', contextPath='null', encode='null', endpoint='null', namespace='612ffc01-c1b8-4698-9a3d-2d4f9404694f', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='application,insurance-report.yml', group='safes', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=true}}
2025-07-07 09:25:17.284 [] INFO [main] com.alibaba.boot.nacos.config.util.NacosConfigPropertiesUtils[47] nacosConfigProperties : NacosConfigProperties{serverAddr='http://mse-b81bd222-nacos-ans.mse.aliyuncs.com', contextPath='null', encode='null', endpoint='null', namespace='612ffc01-c1b8-4698-9a3d-2d4f9404694f', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='application,insurance-report.yml', group='safes', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=true}}
2025-07-07 09:25:17.286 [] INFO [main] com.cfpamf.ms.insur.report.InsuranceReportBizApplication[675] No active profile set, falling back to default profiles: default
2025-07-07 09:25:20.800 [] INFO [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate[244] Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-07 09:25:20.805 [] INFO [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate[126] Bootstrapping Spring Data repositories in DEFAULT mode.
2025-07-07 09:25:20.882 [] INFO [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate[182] Finished Spring Data repository scanning in 47ms. Found 0 repository interfaces.
2025-07-07 09:25:21.638 [] INFO [main] org.springframework.cloud.context.scope.GenericScope[295] BeanFactory id=a1f1679d-0530-3251-ab13-40e2933b35d6
2025-07-07 09:25:21.691 [] INFO [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor[48] Post-processing PropertySource instances
2025-07-07 09:25:21.800 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-07-07 09:25:21.801 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-07-07 09:25:21.801 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-07-07 09:25:21.802 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource systemProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 09:25:21.802 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 09:25:21.803 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-07 09:25:21.803 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource applicationConfig: [classpath:/application.properties] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 09:25:21.803 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 09:25:21.804 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource application|safes|612ffc01-c1b8-4698-9a3d-2d4f9404694f||http://mse-b81bd222-nacos-ans.mse.aliyuncs.com||||||| [com.alibaba.nacos.spring.core.env.NacosPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 09:25:21.804 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource insurance-report.yml|safes|612ffc01-c1b8-4698-9a3d-2d4f9404694f||http://mse-b81bd222-nacos-ans.mse.aliyuncs.com||||||| [com.alibaba.nacos.spring.core.env.NacosPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 09:25:21.805 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource applicationConfig: [classpath:/bootstrap.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 09:25:21.805 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 09:25:23.037 [] INFO [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[330] Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$a93af6ce] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-07 09:25:24.865 [] INFO [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver[34] Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-07 09:25:24.870 [] INFO [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector[31] Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-07 09:25:25.659 [] INFO [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer[90] Tomcat initialized with port(s): 10120 (http)
2025-07-07 09:25:25.687 [] INFO [main] org.apache.coyote.http11.Http11NioProtocol[173] Initializing ProtocolHandler ["http-nio-10120"]
2025-07-07 09:25:25.704 [] INFO [main] org.apache.catalina.core.StandardService[173] Starting service [Tomcat]
2025-07-07 09:25:25.704 [] INFO [main] org.apache.catalina.core.StandardEngine[173] Starting Servlet engine: [Apache Tomcat/9.0.16]
2025-07-07 09:25:25.729 [] INFO [main] org.apache.catalina.core.AprLifecycleListener[173] The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.]
2025-07-07 09:25:25.969 [] INFO [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/][173] Initializing Spring embedded WebApplicationContext
2025-07-07 09:25:25.969 [] INFO [main] org.springframework.web.context.ContextLoader[296] Root WebApplicationContext: initialization completed in 8602 ms
2025-07-07 09:25:27.609 [] DEBUG [main] com.cfpamf.ms.insur.report.config.RedisConfig[65] 自定义RedisCacheManager加载完成
2025-07-07 09:25:27.722 [] INFO [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor[342] Inconsistent constructor declaration on bean with name 'dataSourceDwConfig': single autowire-marked constructor flagged as optional - this constructor is effectively required since there is no default constructor to fall back to: public com.cfpamf.ms.insur.report.config.DataSourceDwConfig$$EnhancerBySpringCGLIB$$5ce990f6(org.mybatis.spring.boot.autoconfigure.MybatisProperties)
2025-07-07 09:25:28.166 [] INFO [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor[342] Inconsistent constructor declaration on bean with name 'dataSourceOdpsConfig': single autowire-marked constructor flagged as optional - this constructor is effectively required since there is no default constructor to fall back to: public com.cfpamf.ms.insur.report.config.DataSourceOdpsConfig$$EnhancerBySpringCGLIB$$b9c5bb5b(org.mybatis.spring.boot.autoconfigure.MybatisProperties)
2025-07-07 09:25:28.242 [] INFO [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor[342] Inconsistent constructor declaration on bean with name 'dataSourceSafesConfig': single autowire-marked constructor flagged as optional - this constructor is effectively required since there is no default constructor to fall back to: public com.cfpamf.ms.insur.report.config.DataSourceSafesConfig$$EnhancerBySpringCGLIB$$77ef9e6d(org.mybatis.spring.boot.autoconfigure.MybatisProperties)
2025-07-07 09:25:28.341 [] INFO [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor[342] Inconsistent constructor declaration on bean with name 'dataSourceSafesPgConfig': single autowire-marked constructor flagged as optional - this constructor is effectively required since there is no default constructor to fall back to: public com.cfpamf.ms.insur.report.config.DataSourceSafesPgConfig$$EnhancerBySpringCGLIB$$22fab524(org.mybatis.spring.boot.autoconfigure.MybatisProperties)
2025-07-07 09:25:30.886 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] classicInsuranceAmountSummaryService支持的诊断类型为CLASSIC_INSURANCE_AMOUNT_SUMMARY,加载成功！
2025-07-07 09:25:30.887 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] diagnosisAndConclusionService支持的诊断类型为DIAGNOSIS_AND_CONCLUSION,加载成功！
2025-07-07 09:25:30.887 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] insuranceAmountRateSummaryService支持的诊断类型为INSURANCE_AMOUNT_RATE_SUMMARY,加载成功！
2025-07-07 09:25:30.887 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] retentionRateSummaryService支持的诊断类型为RETENTION_RATE_SUMMARY,加载成功！
2025-07-07 09:25:31.183 [] DEBUG [main] com.cfpamf.ms.insur.report.config.RedisConfig[77] 自定义RedisTemplate加载完成
2025-07-07 09:25:32.024 [] INFO [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver[59] Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-07 09:25:32.616 [] INFO [main] io.lettuce.core.EpollProvider[68] Starting without optional epoll library
2025-07-07 09:25:32.619 [] INFO [main] io.lettuce.core.KqueueProvider[70] Starting without optional kqueue library
2025-07-07 09:25:33.166 [] INFO [main] springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping[69] Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2025-07-07 09:25:33.287 [] INFO [main] com.netflix.config.sources.URLConfigurationSource[122] To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-07 09:25:33.299 [] INFO [main] com.netflix.config.sources.URLConfigurationSource[122] To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-07 09:25:33.738 [] INFO [main] org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor[171] Initializing ExecutorService 'applicationTaskExecutor'
2025-07-07 09:25:34.609 [] INFO [main] tk.mybatis.mapper.autoconfigure.MapperCacheDisabler[60] Clear tk.mybatis.mapper.util.MsUtil CLASS_CACHE cache.
2025-07-07 09:25:34.611 [] INFO [main] tk.mybatis.mapper.autoconfigure.MapperCacheDisabler[60] Clear tk.mybatis.mapper.genid.GenIdUtil CACHE cache.
2025-07-07 09:25:34.612 [] INFO [main] tk.mybatis.mapper.autoconfigure.MapperCacheDisabler[60] Clear tk.mybatis.mapper.version.VersionUtil CACHE cache.
2025-07-07 09:25:34.614 [] INFO [main] tk.mybatis.mapper.autoconfigure.MapperCacheDisabler[83] Clear EntityHelper entityTableMap cache.
2025-07-07 09:25:36.512 [] INFO [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper[160] Context refreshed
2025-07-07 09:25:36.569 [] INFO [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper[163] Found 1 custom documentation plugin(s)
2025-07-07 09:25:36.644 [] INFO [main] springfox.documentation.spring.web.scanners.ApiListingReferenceScanner[41] Scanning for api listing references
2025-07-07 09:25:38.334 [] INFO [main] org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor[295] No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-07-07 09:25:38.364 [] INFO [main] org.apache.coyote.http11.Http11NioProtocol[173] Starting ProtocolHandler ["http-nio-10120"]
2025-07-07 09:25:38.391 [] INFO [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer[204] Tomcat started on port(s): 10120 (http) with context path ''
2025-07-07 09:25:38.397 [] INFO [main] com.cfpamf.ms.insur.report.InsuranceReportBizApplication[59] Started InsuranceReportBizApplication in 23.86 seconds (JVM running for 24.793)
2025-07-07 09:34:10.400 [] INFO [http-nio-10120-exec-1] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/][173] Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 09:34:10.411 [] INFO [http-nio-10120-exec-1] org.springframework.web.servlet.DispatcherServlet[524] Initializing Servlet 'dispatcherServlet'
2025-07-07 09:34:10.697 [] INFO [http-nio-10120-exec-1] org.springframework.web.servlet.DispatcherServlet[546] Completed initialization in 285 ms
2025-07-07 09:58:24.991 [] INFO [Thread-17] org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor[208] Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-07 09:58:26.267 [] INFO [Thread-17] com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor[288] class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-07-07 09:58:26.274 [] INFO [Thread-17] com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor[288] class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-07-07 09:58:33.580 [] INFO [main] com.alibaba.boot.nacos.config.util.NacosConfigPropertiesUtils[47] nacosConfigProperties : NacosConfigProperties{serverAddr='http://mse-b81bd222-nacos-ans.mse.aliyuncs.com', contextPath='null', encode='null', endpoint='null', namespace='612ffc01-c1b8-4698-9a3d-2d4f9404694f', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='application,insurance-report.yml', group='safes', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=true}}
2025-07-07 09:58:34.838 [] INFO [main] com.alibaba.boot.nacos.config.util.NacosConfigPropertiesUtils[47] nacosConfigProperties : NacosConfigProperties{serverAddr='http://mse-b81bd222-nacos-ans.mse.aliyuncs.com', contextPath='null', encode='null', endpoint='null', namespace='612ffc01-c1b8-4698-9a3d-2d4f9404694f', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='application,insurance-report.yml', group='safes', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=true}}
2025-07-07 09:58:34.841 [] INFO [main] com.cfpamf.ms.insur.report.InsuranceReportBizApplication[675] No active profile set, falling back to default profiles: default
2025-07-07 09:58:37.719 [] INFO [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate[244] Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-07 09:58:37.725 [] INFO [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate[126] Bootstrapping Spring Data repositories in DEFAULT mode.
2025-07-07 09:58:37.809 [] INFO [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate[182] Finished Spring Data repository scanning in 57ms. Found 0 repository interfaces.
2025-07-07 09:58:38.477 [] INFO [main] org.springframework.cloud.context.scope.GenericScope[295] BeanFactory id=a1f1679d-0530-3251-ab13-40e2933b35d6
2025-07-07 09:58:38.518 [] INFO [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor[48] Post-processing PropertySource instances
2025-07-07 09:58:38.600 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-07-07 09:58:38.600 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-07-07 09:58:38.601 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-07-07 09:58:38.601 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource systemProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 09:58:38.601 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 09:58:38.602 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-07 09:58:38.602 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource applicationConfig: [classpath:/application.properties] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 09:58:38.602 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 09:58:38.602 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource application|safes|612ffc01-c1b8-4698-9a3d-2d4f9404694f||http://mse-b81bd222-nacos-ans.mse.aliyuncs.com||||||| [com.alibaba.nacos.spring.core.env.NacosPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 09:58:38.602 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource insurance-report.yml|safes|612ffc01-c1b8-4698-9a3d-2d4f9404694f||http://mse-b81bd222-nacos-ans.mse.aliyuncs.com||||||| [com.alibaba.nacos.spring.core.env.NacosPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 09:58:38.603 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource applicationConfig: [classpath:/bootstrap.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 09:58:38.603 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 09:58:39.052 [] INFO [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[330] Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$bb051c25] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-07 09:58:39.560 [] INFO [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver[34] Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-07 09:58:39.564 [] INFO [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector[31] Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-07 09:58:40.193 [] INFO [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer[90] Tomcat initialized with port(s): 10120 (http)
2025-07-07 09:58:40.222 [] INFO [main] org.apache.coyote.http11.Http11NioProtocol[173] Initializing ProtocolHandler ["http-nio-10120"]
2025-07-07 09:58:40.237 [] INFO [main] org.apache.catalina.core.StandardService[173] Starting service [Tomcat]
2025-07-07 09:58:40.238 [] INFO [main] org.apache.catalina.core.StandardEngine[173] Starting Servlet engine: [Apache Tomcat/9.0.16]
2025-07-07 09:58:40.255 [] INFO [main] org.apache.catalina.core.AprLifecycleListener[173] The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.]
2025-07-07 09:58:40.496 [] INFO [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/][173] Initializing Spring embedded WebApplicationContext
2025-07-07 09:58:40.497 [] INFO [main] org.springframework.web.context.ContextLoader[296] Root WebApplicationContext: initialization completed in 5525 ms
2025-07-07 09:58:42.163 [] DEBUG [main] com.cfpamf.ms.insur.report.config.RedisConfig[65] 自定义RedisCacheManager加载完成
2025-07-07 09:58:42.270 [] INFO [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor[342] Inconsistent constructor declaration on bean with name 'dataSourceDwConfig': single autowire-marked constructor flagged as optional - this constructor is effectively required since there is no default constructor to fall back to: public com.cfpamf.ms.insur.report.config.DataSourceDwConfig$$EnhancerBySpringCGLIB$$6eb3b64d(org.mybatis.spring.boot.autoconfigure.MybatisProperties)
2025-07-07 09:58:42.698 [] INFO [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor[342] Inconsistent constructor declaration on bean with name 'dataSourceOdpsConfig': single autowire-marked constructor flagged as optional - this constructor is effectively required since there is no default constructor to fall back to: public com.cfpamf.ms.insur.report.config.DataSourceOdpsConfig$$EnhancerBySpringCGLIB$$cb8fe0b2(org.mybatis.spring.boot.autoconfigure.MybatisProperties)
2025-07-07 09:58:42.795 [] INFO [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor[342] Inconsistent constructor declaration on bean with name 'dataSourceSafesConfig': single autowire-marked constructor flagged as optional - this constructor is effectively required since there is no default constructor to fall back to: public com.cfpamf.ms.insur.report.config.DataSourceSafesConfig$$EnhancerBySpringCGLIB$$89b9c3c4(org.mybatis.spring.boot.autoconfigure.MybatisProperties)
2025-07-07 09:58:42.876 [] INFO [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor[342] Inconsistent constructor declaration on bean with name 'dataSourceSafesPgConfig': single autowire-marked constructor flagged as optional - this constructor is effectively required since there is no default constructor to fall back to: public com.cfpamf.ms.insur.report.config.DataSourceSafesPgConfig$$EnhancerBySpringCGLIB$$34c4da7b(org.mybatis.spring.boot.autoconfigure.MybatisProperties)
2025-07-07 09:58:45.620 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] classicInsuranceAmountSummaryService支持的诊断类型为CLASSIC_INSURANCE_AMOUNT_SUMMARY,加载成功！
2025-07-07 09:58:45.621 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] diagnosisAndConclusionService支持的诊断类型为DIAGNOSIS_AND_CONCLUSION,加载成功！
2025-07-07 09:58:45.621 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] insuranceAmountRateSummaryService支持的诊断类型为INSURANCE_AMOUNT_RATE_SUMMARY,加载成功！
2025-07-07 09:58:45.621 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] retentionRateSummaryService支持的诊断类型为RETENTION_RATE_SUMMARY,加载成功！
2025-07-07 09:58:46.004 [] DEBUG [main] com.cfpamf.ms.insur.report.config.RedisConfig[77] 自定义RedisTemplate加载完成
2025-07-07 09:58:47.169 [] INFO [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver[59] Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-07 09:58:47.895 [] INFO [main] io.lettuce.core.EpollProvider[68] Starting without optional epoll library
2025-07-07 09:58:47.898 [] INFO [main] io.lettuce.core.KqueueProvider[70] Starting without optional kqueue library
2025-07-07 09:58:48.516 [] INFO [main] springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping[69] Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2025-07-07 09:58:48.640 [] INFO [main] com.netflix.config.sources.URLConfigurationSource[122] To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-07 09:58:48.652 [] INFO [main] com.netflix.config.sources.URLConfigurationSource[122] To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-07 09:58:49.160 [] INFO [main] org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor[171] Initializing ExecutorService 'applicationTaskExecutor'
2025-07-07 09:58:50.024 [] INFO [main] tk.mybatis.mapper.autoconfigure.MapperCacheDisabler[60] Clear tk.mybatis.mapper.util.MsUtil CLASS_CACHE cache.
2025-07-07 09:58:50.025 [] INFO [main] tk.mybatis.mapper.autoconfigure.MapperCacheDisabler[60] Clear tk.mybatis.mapper.genid.GenIdUtil CACHE cache.
2025-07-07 09:58:50.028 [] INFO [main] tk.mybatis.mapper.autoconfigure.MapperCacheDisabler[60] Clear tk.mybatis.mapper.version.VersionUtil CACHE cache.
2025-07-07 09:58:50.031 [] INFO [main] tk.mybatis.mapper.autoconfigure.MapperCacheDisabler[83] Clear EntityHelper entityTableMap cache.
2025-07-07 09:58:51.692 [] INFO [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper[160] Context refreshed
2025-07-07 09:58:51.733 [] INFO [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper[163] Found 1 custom documentation plugin(s)
2025-07-07 09:58:51.796 [] INFO [main] springfox.documentation.spring.web.scanners.ApiListingReferenceScanner[41] Scanning for api listing references
2025-07-07 09:58:53.500 [] INFO [main] org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor[295] No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-07-07 09:58:53.537 [] INFO [main] org.apache.coyote.http11.Http11NioProtocol[173] Starting ProtocolHandler ["http-nio-10120"]
2025-07-07 09:58:53.578 [] INFO [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer[204] Tomcat started on port(s): 10120 (http) with context path ''
2025-07-07 09:58:53.588 [] INFO [main] com.cfpamf.ms.insur.report.InsuranceReportBizApplication[59] Started InsuranceReportBizApplication in 21.638 seconds (JVM running for 23.931)
2025-07-07 10:17:07.901 [] INFO [lettuce-eventExecutorLoop-1-8] io.lettuce.core.protocol.ConnectionWatchdog[173] Reconnecting, last destination was r-2zef4d5b5cdab4e4.redis.rds.aliyuncs.com/192.168.6.16:6379
2025-07-07 10:17:08.054 [] INFO [lettuce-nioEventLoop-4-3] io.lettuce.core.protocol.ReconnectionHandler[177] Reconnected to r-2zef4d5b5cdab4e4.redis.rds.aliyuncs.com:6379
2025-07-07 10:29:44.404 [] INFO [lettuce-eventExecutorLoop-1-5] io.lettuce.core.protocol.ConnectionWatchdog[173] Reconnecting, last destination was r-2zef4d5b5cdab4e4.redis.rds.aliyuncs.com/192.168.6.16:6379
2025-07-07 10:29:44.440 [] INFO [Thread-17] org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor[208] Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-07 10:29:44.515 [] INFO [lettuce-nioEventLoop-4-4] io.lettuce.core.protocol.ReconnectionHandler[177] Reconnected to r-2zef4d5b5cdab4e4.redis.rds.aliyuncs.com:6379
2025-07-07 10:29:45.750 [] INFO [Thread-17] com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor[288] class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-07-07 10:29:45.756 [] INFO [Thread-17] com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor[288] class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-07-07 10:29:56.467 [] INFO [main] com.alibaba.boot.nacos.config.util.NacosConfigPropertiesUtils[47] nacosConfigProperties : NacosConfigProperties{serverAddr='http://mse-b81bd222-nacos-ans.mse.aliyuncs.com', contextPath='null', encode='null', endpoint='null', namespace='612ffc01-c1b8-4698-9a3d-2d4f9404694f', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='application,insurance-report.yml', group='safes', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=true}}
2025-07-07 10:29:57.539 [] INFO [main] com.alibaba.boot.nacos.config.util.NacosConfigPropertiesUtils[47] nacosConfigProperties : NacosConfigProperties{serverAddr='http://mse-b81bd222-nacos-ans.mse.aliyuncs.com', contextPath='null', encode='null', endpoint='null', namespace='612ffc01-c1b8-4698-9a3d-2d4f9404694f', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='application,insurance-report.yml', group='safes', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=true}}
2025-07-07 10:29:57.542 [] INFO [main] com.cfpamf.ms.insur.report.InsuranceReportBizApplication[675] No active profile set, falling back to default profiles: default
2025-07-07 10:30:01.111 [] INFO [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate[244] Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-07 10:30:01.116 [] INFO [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate[126] Bootstrapping Spring Data repositories in DEFAULT mode.
2025-07-07 10:30:01.208 [] INFO [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate[182] Finished Spring Data repository scanning in 67ms. Found 0 repository interfaces.
2025-07-07 10:30:01.891 [] INFO [main] org.springframework.cloud.context.scope.GenericScope[295] BeanFactory id=323dedff-86ed-336d-bca0-80ceef4b44c5
2025-07-07 10:30:01.937 [] INFO [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor[48] Post-processing PropertySource instances
2025-07-07 10:30:02.031 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-07-07 10:30:02.032 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-07-07 10:30:02.032 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-07-07 10:30:02.033 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource systemProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 10:30:02.033 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 10:30:02.033 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-07 10:30:02.034 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource applicationConfig: [classpath:/application.properties] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 10:30:02.034 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 10:30:02.034 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource application|safes|612ffc01-c1b8-4698-9a3d-2d4f9404694f||http://mse-b81bd222-nacos-ans.mse.aliyuncs.com||||||| [com.alibaba.nacos.spring.core.env.NacosPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 10:30:02.035 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource insurance-report.yml|safes|612ffc01-c1b8-4698-9a3d-2d4f9404694f||http://mse-b81bd222-nacos-ans.mse.aliyuncs.com||||||| [com.alibaba.nacos.spring.core.env.NacosPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 10:30:02.036 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource applicationConfig: [classpath:/bootstrap.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 10:30:02.036 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 10:30:02.630 [] INFO [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[330] Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$24a84fad] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-07 10:30:03.124 [] INFO [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver[34] Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-07 10:30:03.128 [] INFO [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector[31] Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-07 10:30:03.771 [] INFO [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer[90] Tomcat initialized with port(s): 10120 (http)
2025-07-07 10:30:03.801 [] INFO [main] org.apache.coyote.http11.Http11NioProtocol[173] Initializing ProtocolHandler ["http-nio-10120"]
2025-07-07 10:30:03.817 [] INFO [main] org.apache.catalina.core.StandardService[173] Starting service [Tomcat]
2025-07-07 10:30:03.819 [] INFO [main] org.apache.catalina.core.StandardEngine[173] Starting Servlet engine: [Apache Tomcat/9.0.16]
2025-07-07 10:30:03.837 [] INFO [main] org.apache.catalina.core.AprLifecycleListener[173] The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.]
2025-07-07 10:30:04.096 [] INFO [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/][173] Initializing Spring embedded WebApplicationContext
2025-07-07 10:30:04.096 [] INFO [main] org.springframework.web.context.ContextLoader[296] Root WebApplicationContext: initialization completed in 6453 ms
2025-07-07 10:30:05.611 [] DEBUG [main] com.cfpamf.ms.insur.report.config.RedisConfig[65] 自定义RedisCacheManager加载完成
2025-07-07 10:30:05.740 [] INFO [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor[342] Inconsistent constructor declaration on bean with name 'dataSourceDwConfig': single autowire-marked constructor flagged as optional - this constructor is effectively required since there is no default constructor to fall back to: public com.cfpamf.ms.insur.report.config.DataSourceDwConfig$$EnhancerBySpringCGLIB$$d856e9d5(org.mybatis.spring.boot.autoconfigure.MybatisProperties)
2025-07-07 10:30:06.260 [] INFO [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor[342] Inconsistent constructor declaration on bean with name 'dataSourceOdpsConfig': single autowire-marked constructor flagged as optional - this constructor is effectively required since there is no default constructor to fall back to: public com.cfpamf.ms.insur.report.config.DataSourceOdpsConfig$$EnhancerBySpringCGLIB$$3533143a(org.mybatis.spring.boot.autoconfigure.MybatisProperties)
2025-07-07 10:30:06.402 [] INFO [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor[342] Inconsistent constructor declaration on bean with name 'dataSourceSafesConfig': single autowire-marked constructor flagged as optional - this constructor is effectively required since there is no default constructor to fall back to: public com.cfpamf.ms.insur.report.config.DataSourceSafesConfig$$EnhancerBySpringCGLIB$$f35cf74c(org.mybatis.spring.boot.autoconfigure.MybatisProperties)
2025-07-07 10:30:06.491 [] INFO [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor[342] Inconsistent constructor declaration on bean with name 'dataSourceSafesPgConfig': single autowire-marked constructor flagged as optional - this constructor is effectively required since there is no default constructor to fall back to: public com.cfpamf.ms.insur.report.config.DataSourceSafesPgConfig$$EnhancerBySpringCGLIB$$9e680e03(org.mybatis.spring.boot.autoconfigure.MybatisProperties)
2025-07-07 10:30:09.801 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] classicInsuranceAmountSummaryService支持的诊断类型为CLASSIC_INSURANCE_AMOUNT_SUMMARY,加载成功！
2025-07-07 10:30:09.802 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] diagnosisAndConclusionService支持的诊断类型为DIAGNOSIS_AND_CONCLUSION,加载成功！
2025-07-07 10:30:09.802 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] insuranceAmountRateSummaryService支持的诊断类型为INSURANCE_AMOUNT_RATE_SUMMARY,加载成功！
2025-07-07 10:30:09.802 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] retentionRateSummaryService支持的诊断类型为RETENTION_RATE_SUMMARY,加载成功！
2025-07-07 10:30:10.263 [] DEBUG [main] com.cfpamf.ms.insur.report.config.RedisConfig[77] 自定义RedisTemplate加载完成
2025-07-07 10:30:11.406 [] INFO [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver[59] Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-07 10:30:12.153 [] INFO [main] io.lettuce.core.EpollProvider[68] Starting without optional epoll library
2025-07-07 10:30:12.157 [] INFO [main] io.lettuce.core.KqueueProvider[70] Starting without optional kqueue library
2025-07-07 10:30:12.778 [] INFO [main] springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping[69] Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2025-07-07 10:30:12.920 [] INFO [main] com.netflix.config.sources.URLConfigurationSource[122] To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-07 10:30:12.930 [] INFO [main] com.netflix.config.sources.URLConfigurationSource[122] To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-07 10:30:13.604 [] INFO [main] org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor[171] Initializing ExecutorService 'applicationTaskExecutor'
2025-07-07 10:30:14.651 [] INFO [main] tk.mybatis.mapper.autoconfigure.MapperCacheDisabler[60] Clear tk.mybatis.mapper.util.MsUtil CLASS_CACHE cache.
2025-07-07 10:30:14.652 [] INFO [main] tk.mybatis.mapper.autoconfigure.MapperCacheDisabler[60] Clear tk.mybatis.mapper.genid.GenIdUtil CACHE cache.
2025-07-07 10:30:14.653 [] INFO [main] tk.mybatis.mapper.autoconfigure.MapperCacheDisabler[60] Clear tk.mybatis.mapper.version.VersionUtil CACHE cache.
2025-07-07 10:30:14.656 [] INFO [main] tk.mybatis.mapper.autoconfigure.MapperCacheDisabler[83] Clear EntityHelper entityTableMap cache.
2025-07-07 10:30:16.104 [] INFO [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper[160] Context refreshed
2025-07-07 10:30:16.146 [] INFO [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper[163] Found 1 custom documentation plugin(s)
2025-07-07 10:30:16.199 [] INFO [main] springfox.documentation.spring.web.scanners.ApiListingReferenceScanner[41] Scanning for api listing references
2025-07-07 10:30:17.889 [] INFO [main] org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor[295] No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-07-07 10:30:17.922 [] INFO [main] org.apache.coyote.http11.Http11NioProtocol[173] Starting ProtocolHandler ["http-nio-10120"]
2025-07-07 10:30:17.950 [] INFO [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer[204] Tomcat started on port(s): 10120 (http) with context path ''
2025-07-07 10:30:17.957 [] INFO [main] com.cfpamf.ms.insur.report.InsuranceReportBizApplication[59] Started InsuranceReportBizApplication in 23.149 seconds (JVM running for 24.293)
2025-07-07 10:30:45.785 [] INFO [http-nio-10120-exec-1] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/][173] Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 10:30:45.786 [] INFO [http-nio-10120-exec-1] org.springframework.web.servlet.DispatcherServlet[524] Initializing Servlet 'dispatcherServlet'
2025-07-07 10:30:45.819 [] INFO [http-nio-10120-exec-1] org.springframework.web.servlet.DispatcherServlet[546] Completed initialization in 32 ms
2025-07-07 10:32:01.190 [] INFO [http-nio-10120-exec-10] com.cfpamf.ms.insur.report.advice.GlobalExceptionHandler[37] 业务正常校验异常
com.cfpamf.common.ms.exception.MSBizNormalException: 无法获取网关token
	at com.cfpamf.ms.insur.report.service.BmsService.getToken(BmsService.java:78)
	at com.cfpamf.ms.insur.report.service.BmsService.getContextUserDetail(BmsService.java:67)
	at com.cfpamf.ms.insur.report.web.AssistantSalerNewController.pageEmpAssessConvertRankMonth(AssistantSalerNewController.java:61)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:189)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:102)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:800)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1038)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:665)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.cfpamf.ms.insur.report.advice.RequestFilter.doFilter(RequestFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:123)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:151)
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:81)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-07 10:33:23.273 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] ---> GET http://bms-service.tsg.cfpamf.com/user/detail/safes HTTP/1.1
2025-07-07 10:33:23.274 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] authorization: eyJhbGciOiJIUzUxMiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NshtIKKX-662IIkxaqRT3Z2cDCPsxMxoHlG-p6mfclCEoh_CDU2yylpAlM64rTMlkA7Kg2RMCAPBtzWxq6_N5g
2025-07-07 10:33:23.274 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] ---> END HTTP (0-byte body)
2025-07-07 10:33:23.561 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] <--- HTTP/1.1 200 OK (286ms)
2025-07-07 10:33:23.562 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] content-length: 13889
2025-07-07 10:33:23.563 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] content-type: application/json
2025-07-07 10:33:23.564 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] date: Mon, 07 Jul 2025 02:33:24 GMT
2025-07-07 10:33:23.565 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] server: istio-envoy
2025-07-07 10:33:23.566 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] x-application-context: bms-service-biz:test:10027
2025-07-07 10:33:23.566 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] x-envoy-upstream-service-time: 83
2025-07-07 10:33:23.567 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] 
2025-07-07 10:33:23.584 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] {
	"code":"",
	"data":{
		"areaOrgCode":"",
		"batchNo":"20220714005",
		"birthday":-209606400000,
		"createMethod":"hr",
		"description":"",
		"deviceId":"",
		"employeeId":14,
		"employeeName":"何国强",
		"employeeStatus":3,
		"employeeType":0,
		"entryDate":1651161600000,
		"entrySystemDate":1524196438000,
		"gray":0,
		"hrOrgId":447942,
		"hrOrgTreePath":"*********/282717/360674/447942",
		"hrPostId":156932,
		"hrUserId":107369318,
		"idCard":"220881196305120848",
		"isHeadOrg":true,
		"jianzhiJobNumber":"",
		"jobCode":"**********",
		"jobNumber":"CNBJ0394",
		"lastWorkDate":null,
		"mobile":"***********",
		"officeTel":"",
		"orgCode":"204",
		"orgId":669,
		"orgName":"财务部",
		"passwordLevel":2,
		"postId":221,
		"postList":[],
		"postName":"数据开发岗",
		"purpose":"",
		"roleList":[
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0012",
				"roleId":10044,
				"roleName":"信息技术部",
				"systemId":"6",
				"systemShortName":"信贷管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0009",
				"roleId":10031,
				"roleName":"总部员工",
				"systemId":"6",
				"systemShortName":"信贷管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0038",
				"roleId":10111,
				"roleName":"全量数据查看",
				"systemId":"6",
				"systemShortName":"信贷管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0017",
				"roleId":10033,
				"roleName":"中/高级管理层",
				"systemId":"6",
				"systemShortName":"信贷管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0003",
				"roleId":3,
				"roleName":"总部职能部门负责人",
				"systemId":"2",
				"systemShortName":"运营管理平台",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10160,
				"roleName":"系统管理员",
				"systemId":"11",
				"systemShortName":"电商支撑运营系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10160,
				"roleName":"系统管理员",
				"systemId":"11",
				"systemShortName":"电商支撑运营系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R1000",
				"roleId":10002,
				"roleName":"保险|管理员",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R1000",
				"roleId":10002,
				"roleName":"保险|管理员",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10381,
				"roleName":"保险|管理员新",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10381,
				"roleName":"保险|管理员新",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10125,
				"roleName":"系统超级管理员",
				"systemId":"8",
				"systemShortName":"HR系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0066",
				"roleId":10422,
				"roleName":"人力总监",
				"systemId":"8",
				"systemShortName":"HR系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0067",
				"roleId":10436,
				"roleName":"凤媛01",
				"systemId":"8",
				"systemShortName":"HR系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0049",
				"roleId":10387,
				"roleName":"保险|渠道pco",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0005",
				"roleId":10154,
				"roleName":"技术组",
				"systemId":"11",
				"systemShortName":"电商支撑运营系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10398,
				"roleName":"超级管理员",
				"systemId":"35",
				"systemShortName":"电商平台运营系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0004",
				"roleId":10005,
				"roleName":"保险|理赔管理",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0022",
				"roleId":10190,
				"roleName":"保险|子管理员",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10001,
				"roleName":"系统管理员",
				"systemId":"1",
				"systemShortName":"基础管理平台",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10335,
				"roleName":"系统管理员",
				"systemId":"19",
				"systemShortName":"营销平台",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0009",
				"roleId":10911,
				"roleName":"总部员工",
				"systemId":"70",
				"systemShortName":"掌上中和",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0017",
				"roleId":10919,
				"roleName":"中/高级管理层",
				"systemId":"70",
				"systemShortName":"掌上中和",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0012",
				"roleId":10914,
				"roleName":"信息技术部",
				"systemId":"70",
				"systemShortName":"掌上中和",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0038",
				"roleId":10938,
				"roleName":"全量数据查看",
				"systemId":"70",
				"systemShortName":"掌上中和",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0004",
				"roleId":4,
				"roleName":"总部普通员工",
				"systemId":"2",
				"systemShortName":"运营管理平台",
				"userRoleType":2
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0017",
				"roleId":17,
				"roleName":"数字技术中心",
				"systemId":"2",
				"systemShortName":"运营管理平台",
				"userRoleType":2
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0009",
				"roleId":10031,
				"roleName":"总部员工",
				"systemId":"6",
				"systemShortName":"信贷管理系统",
				"userRoleType":2
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0038",
				"roleId":10111,
				"roleName":"全量数据查看",
				"systemId":"6",
				"systemShortName":"信贷管理系统",
				"userRoleType":2
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0014",
				"roleId":10138,
				"roleName":"普通员工",
				"systemId":"8",
				"systemShortName":"HR系统",
				"userRoleType":2
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0002",
				"roleId":10003,
				"roleName":"保险|业务管理",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":2
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0046",
				"roleId":10627,
				"roleName":"ERP-库存管理",
				"systemId":"11",
				"systemShortName":"电商支撑运营系统",
				"userRoleType":2
			}
		],
		"serviceType":0,
		"updateTime":*************,
		"userAccount":"***********",
		"userAdminId":"**********",
		"userAdminName":"吴玲",
		"userEmail":"",
		"userId":1939,
		"userMasterId":"0",
		"userMasterName":"",
		"userPartTimerList":null,
		"userSex":1,
		"userStatus":1
	},
	"errorCode":"",
	"errorContext":null,
	"errorMsg":"",
	"message":"",
	"success":true
}
2025-07-07 10:33:23.588 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] <--- END HTTP (13889-byte body)
2025-07-07 10:33:23.698 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] ---> GET http://insurance-admin.tsg.cfpamf.com/back/data/auth/dataAuth?authorization=eyJhbGciOiJIUzUxMiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NshtIKKX-662IIkxaqRT3Z2cDCPsxMxoHlG-p6mfclCEoh_CDU2yylpAlM64rTMlkA7Kg2RMCAPBtzWxq6_N5g HTTP/1.1
2025-07-07 10:33:23.699 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] ---> END HTTP (0-byte body)
2025-07-07 10:33:24.010 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] <--- HTTP/1.1 200 OK (310ms)
2025-07-07 10:33:24.011 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] content-type: application/json;charset=UTF-8
2025-07-07 10:33:24.012 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] date: Mon, 07 Jul 2025 02:33:24 GMT
2025-07-07 10:33:24.014 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] server: istio-envoy
2025-07-07 10:33:24.014 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] transfer-encoding: chunked
2025-07-07 10:33:24.015 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] x-envoy-upstream-service-time: 138
2025-07-07 10:33:24.016 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] 
2025-07-07 10:33:24.021 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] {"code":"0","traceId":"","message":"success","data":{"userId":null,"agentId":null,"regionName":null,"regionCode":null,"orgName":null,"orgCode":null,"orgAdmin":null,"channel":null,"picRole":null,"safeCenter":null,"branchBuName":null,"branchBuCode":null,"zoneName":null,"zoneCode":null,"orgPath":null,"branch":false},"success":true,"exception":null}
2025-07-07 10:33:24.022 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] <--- END HTTP (347-byte body)
2025-07-07 10:33:25.627 [] INFO [http-nio-10120-exec-2] com.alibaba.druid.pool.DruidDataSource[930] {dataSource-1} inited
2025-07-07 10:33:25.747 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpMarketingProgressDfpMapper.listAssessConvertRankCountry_COUNT[159] ==>  Preparing: WITH amt_rank AS (SELECT emp_name, emp_code, bch_name, bch_code, district_name, district_code, area_name, area_code, sy_assess_convert_insurance_amt, RANK() OVER (ORDER BY sy_assess_convert_insurance_amt DESC) rank_in_country FROM report.ads_insurance_emp_marketing_progress_dfp WHERE pt = ? AND (leave_date IS NULL OR TO_DATE(leave_date, 'YYYY-MM-DD') > TO_DATE(?, 'YYYYMMDD'))) SELECT count(0) FROM amt_rank 
2025-07-07 10:33:25.781 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpMarketingProgressDfpMapper.listAssessConvertRankCountry_COUNT[159] ==> Parameters: 20250706(String), 20250706(String)
2025-07-07 10:33:25.908 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpMarketingProgressDfpMapper.listAssessConvertRankCountry_COUNT[159] <==      Total: 1
2025-07-07 10:33:26.010 [] INFO [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.service.assistant.impl.AssistantSalerServiceImpl[439] 缓存排名数据成功: assistant:rank:assessConvert:20250706:YEAR:1:10, 过期时间: 48395秒
2025-07-07 10:40:08.668 [] INFO [Thread-17] org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor[208] Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-07 10:40:08.731 [] INFO [Thread-17] com.alibaba.druid.pool.DruidDataSource[1825] {dataSource-1} closed
2025-07-07 10:40:09.899 [] INFO [Thread-17] com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor[288] class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-07-07 10:40:09.910 [] INFO [Thread-17] com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor[288] class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-07-07 10:47:16.403 [] INFO [main] com.alibaba.boot.nacos.config.util.NacosConfigPropertiesUtils[47] nacosConfigProperties : NacosConfigProperties{serverAddr='http://mse-b81bd222-nacos-ans.mse.aliyuncs.com', contextPath='null', encode='null', endpoint='null', namespace='612ffc01-c1b8-4698-9a3d-2d4f9404694f', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='application,insurance-report.yml', group='safes', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=true}}
2025-07-07 10:47:17.554 [] INFO [main] com.alibaba.boot.nacos.config.util.NacosConfigPropertiesUtils[47] nacosConfigProperties : NacosConfigProperties{serverAddr='http://mse-b81bd222-nacos-ans.mse.aliyuncs.com', contextPath='null', encode='null', endpoint='null', namespace='612ffc01-c1b8-4698-9a3d-2d4f9404694f', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='application,insurance-report.yml', group='safes', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=true}}
2025-07-07 10:47:17.555 [] INFO [main] com.cfpamf.ms.insur.report.InsuranceReportBizApplication[675] No active profile set, falling back to default profiles: default
2025-07-07 10:47:21.435 [] INFO [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate[244] Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-07 10:47:21.444 [] INFO [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate[126] Bootstrapping Spring Data repositories in DEFAULT mode.
2025-07-07 10:47:21.570 [] INFO [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate[182] Finished Spring Data repository scanning in 83ms. Found 0 repository interfaces.
2025-07-07 10:47:22.354 [] INFO [main] org.springframework.cloud.context.scope.GenericScope[295] BeanFactory id=323dedff-86ed-336d-bca0-80ceef4b44c5
2025-07-07 10:47:22.399 [] INFO [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor[48] Post-processing PropertySource instances
2025-07-07 10:47:22.495 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-07-07 10:47:22.496 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-07-07 10:47:22.497 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-07-07 10:47:22.498 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource systemProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 10:47:22.498 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 10:47:22.499 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-07 10:47:22.499 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource applicationConfig: [classpath:/application.properties] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 10:47:22.499 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 10:47:22.499 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource application|safes|612ffc01-c1b8-4698-9a3d-2d4f9404694f||http://mse-b81bd222-nacos-ans.mse.aliyuncs.com||||||| [com.alibaba.nacos.spring.core.env.NacosPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 10:47:22.500 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource insurance-report.yml|safes|612ffc01-c1b8-4698-9a3d-2d4f9404694f||http://mse-b81bd222-nacos-ans.mse.aliyuncs.com||||||| [com.alibaba.nacos.spring.core.env.NacosPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 10:47:22.500 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource applicationConfig: [classpath:/bootstrap.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 10:47:22.500 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 10:47:23.036 [] INFO [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[330] Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$f164f168] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-07 10:47:23.629 [] INFO [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver[34] Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-07 10:47:23.634 [] INFO [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector[31] Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-07 10:47:24.361 [] INFO [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer[90] Tomcat initialized with port(s): 10120 (http)
2025-07-07 10:47:24.392 [] INFO [main] org.apache.coyote.http11.Http11NioProtocol[173] Initializing ProtocolHandler ["http-nio-10120"]
2025-07-07 10:47:24.409 [] INFO [main] org.apache.catalina.core.StandardService[173] Starting service [Tomcat]
2025-07-07 10:47:24.409 [] INFO [main] org.apache.catalina.core.StandardEngine[173] Starting Servlet engine: [Apache Tomcat/9.0.16]
2025-07-07 10:47:24.429 [] INFO [main] org.apache.catalina.core.AprLifecycleListener[173] The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.]
2025-07-07 10:47:24.698 [] INFO [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/][173] Initializing Spring embedded WebApplicationContext
2025-07-07 10:47:24.699 [] INFO [main] org.springframework.web.context.ContextLoader[296] Root WebApplicationContext: initialization completed in 7049 ms
2025-07-07 10:47:26.393 [] DEBUG [main] com.cfpamf.ms.insur.report.config.RedisConfig[65] 自定义RedisCacheManager加载完成
2025-07-07 10:47:26.504 [] INFO [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor[342] Inconsistent constructor declaration on bean with name 'dataSourceDwConfig': single autowire-marked constructor flagged as optional - this constructor is effectively required since there is no default constructor to fall back to: public com.cfpamf.ms.insur.report.config.DataSourceDwConfig$$EnhancerBySpringCGLIB$$a5138b90(org.mybatis.spring.boot.autoconfigure.MybatisProperties)
2025-07-07 10:47:26.929 [] INFO [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor[342] Inconsistent constructor declaration on bean with name 'dataSourceOdpsConfig': single autowire-marked constructor flagged as optional - this constructor is effectively required since there is no default constructor to fall back to: public com.cfpamf.ms.insur.report.config.DataSourceOdpsConfig$$EnhancerBySpringCGLIB$$1efb5f5(org.mybatis.spring.boot.autoconfigure.MybatisProperties)
2025-07-07 10:47:27.016 [] INFO [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor[342] Inconsistent constructor declaration on bean with name 'dataSourceSafesConfig': single autowire-marked constructor flagged as optional - this constructor is effectively required since there is no default constructor to fall back to: public com.cfpamf.ms.insur.report.config.DataSourceSafesConfig$$EnhancerBySpringCGLIB$$c0199907(org.mybatis.spring.boot.autoconfigure.MybatisProperties)
2025-07-07 10:47:27.079 [] INFO [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor[342] Inconsistent constructor declaration on bean with name 'dataSourceSafesPgConfig': single autowire-marked constructor flagged as optional - this constructor is effectively required since there is no default constructor to fall back to: public com.cfpamf.ms.insur.report.config.DataSourceSafesPgConfig$$EnhancerBySpringCGLIB$$6b24afbe(org.mybatis.spring.boot.autoconfigure.MybatisProperties)
2025-07-07 10:47:30.082 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] classicInsuranceAmountSummaryService支持的诊断类型为CLASSIC_INSURANCE_AMOUNT_SUMMARY,加载成功！
2025-07-07 10:47:30.083 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] diagnosisAndConclusionService支持的诊断类型为DIAGNOSIS_AND_CONCLUSION,加载成功！
2025-07-07 10:47:30.084 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] insuranceAmountRateSummaryService支持的诊断类型为INSURANCE_AMOUNT_RATE_SUMMARY,加载成功！
2025-07-07 10:47:30.085 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] retentionRateSummaryService支持的诊断类型为RETENTION_RATE_SUMMARY,加载成功！
2025-07-07 10:47:30.453 [] DEBUG [main] com.cfpamf.ms.insur.report.config.RedisConfig[77] 自定义RedisTemplate加载完成
2025-07-07 10:47:31.514 [] INFO [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver[59] Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-07 10:47:32.219 [] INFO [main] io.lettuce.core.EpollProvider[68] Starting without optional epoll library
2025-07-07 10:47:32.222 [] INFO [main] io.lettuce.core.KqueueProvider[70] Starting without optional kqueue library
2025-07-07 10:47:32.855 [] INFO [main] springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping[69] Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2025-07-07 10:47:33.001 [] INFO [main] com.netflix.config.sources.URLConfigurationSource[122] To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-07 10:47:33.018 [] INFO [main] com.netflix.config.sources.URLConfigurationSource[122] To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-07 10:47:33.575 [] INFO [main] org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor[171] Initializing ExecutorService 'applicationTaskExecutor'
2025-07-07 10:47:34.556 [] INFO [main] tk.mybatis.mapper.autoconfigure.MapperCacheDisabler[60] Clear tk.mybatis.mapper.util.MsUtil CLASS_CACHE cache.
2025-07-07 10:47:34.558 [] INFO [main] tk.mybatis.mapper.autoconfigure.MapperCacheDisabler[60] Clear tk.mybatis.mapper.genid.GenIdUtil CACHE cache.
2025-07-07 10:47:34.559 [] INFO [main] tk.mybatis.mapper.autoconfigure.MapperCacheDisabler[60] Clear tk.mybatis.mapper.version.VersionUtil CACHE cache.
2025-07-07 10:47:34.563 [] INFO [main] tk.mybatis.mapper.autoconfigure.MapperCacheDisabler[83] Clear EntityHelper entityTableMap cache.
2025-07-07 10:47:36.391 [] INFO [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper[160] Context refreshed
2025-07-07 10:47:36.445 [] INFO [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper[163] Found 1 custom documentation plugin(s)
2025-07-07 10:47:36.504 [] INFO [main] springfox.documentation.spring.web.scanners.ApiListingReferenceScanner[41] Scanning for api listing references
2025-07-07 10:47:38.504 [] INFO [main] org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor[295] No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-07-07 10:47:38.546 [] INFO [main] org.apache.coyote.http11.Http11NioProtocol[173] Starting ProtocolHandler ["http-nio-10120"]
2025-07-07 10:47:38.586 [] INFO [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer[204] Tomcat started on port(s): 10120 (http) with context path ''
2025-07-07 10:47:38.595 [] INFO [main] com.cfpamf.ms.insur.report.InsuranceReportBizApplication[59] Started InsuranceReportBizApplication in 24.339 seconds (JVM running for 25.466)
2025-07-07 10:51:28.044 [] INFO [http-nio-10120-exec-1] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/][173] Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 10:51:28.044 [] INFO [http-nio-10120-exec-1] org.springframework.web.servlet.DispatcherServlet[524] Initializing Servlet 'dispatcherServlet'
2025-07-07 10:51:28.084 [] INFO [http-nio-10120-exec-1] org.springframework.web.servlet.DispatcherServlet[546] Completed initialization in 39 ms
2025-07-07 10:51:28.283 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] ---> GET http://bms-service.tsg.cfpamf.com/user/detail/safes HTTP/1.1
2025-07-07 10:51:28.283 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] authorization: eyJhbGciOiJIUzUxMiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NshtIKKX-662IIkxaqRT3Z2cDCPsxMxoHlG-p6mfclCEoh_CDU2yylpAlM64rTMlkA7Kg2RMCAPBtzWxq6_N5g
2025-07-07 10:51:28.284 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] ---> END HTTP (0-byte body)
2025-07-07 10:51:28.528 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] <--- HTTP/1.1 200 OK (243ms)
2025-07-07 10:51:28.529 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] content-length: 13889
2025-07-07 10:51:28.529 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] content-type: application/json
2025-07-07 10:51:28.530 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] date: Mon, 07 Jul 2025 02:51:29 GMT
2025-07-07 10:51:28.531 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] server: istio-envoy
2025-07-07 10:51:28.532 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] x-application-context: bms-service-biz:test:10027
2025-07-07 10:51:28.532 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] x-envoy-upstream-service-time: 76
2025-07-07 10:51:28.533 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] 
2025-07-07 10:51:28.573 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] {
	"code":"",
	"data":{
		"areaOrgCode":"",
		"batchNo":"20220714005",
		"birthday":-209606400000,
		"createMethod":"hr",
		"description":"",
		"deviceId":"",
		"employeeId":14,
		"employeeName":"何国强",
		"employeeStatus":3,
		"employeeType":0,
		"entryDate":1651161600000,
		"entrySystemDate":1524196438000,
		"gray":0,
		"hrOrgId":447942,
		"hrOrgTreePath":"*********/282717/360674/447942",
		"hrPostId":156932,
		"hrUserId":107369318,
		"idCard":"220881196305120848",
		"isHeadOrg":true,
		"jianzhiJobNumber":"",
		"jobCode":"**********",
		"jobNumber":"CNBJ0394",
		"lastWorkDate":null,
		"mobile":"***********",
		"officeTel":"",
		"orgCode":"204",
		"orgId":669,
		"orgName":"财务部",
		"passwordLevel":2,
		"postId":221,
		"postList":[],
		"postName":"数据开发岗",
		"purpose":"",
		"roleList":[
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0012",
				"roleId":10044,
				"roleName":"信息技术部",
				"systemId":"6",
				"systemShortName":"信贷管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0009",
				"roleId":10031,
				"roleName":"总部员工",
				"systemId":"6",
				"systemShortName":"信贷管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0038",
				"roleId":10111,
				"roleName":"全量数据查看",
				"systemId":"6",
				"systemShortName":"信贷管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0017",
				"roleId":10033,
				"roleName":"中/高级管理层",
				"systemId":"6",
				"systemShortName":"信贷管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0003",
				"roleId":3,
				"roleName":"总部职能部门负责人",
				"systemId":"2",
				"systemShortName":"运营管理平台",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10160,
				"roleName":"系统管理员",
				"systemId":"11",
				"systemShortName":"电商支撑运营系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10160,
				"roleName":"系统管理员",
				"systemId":"11",
				"systemShortName":"电商支撑运营系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R1000",
				"roleId":10002,
				"roleName":"保险|管理员",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R1000",
				"roleId":10002,
				"roleName":"保险|管理员",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10381,
				"roleName":"保险|管理员新",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10381,
				"roleName":"保险|管理员新",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10125,
				"roleName":"系统超级管理员",
				"systemId":"8",
				"systemShortName":"HR系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0066",
				"roleId":10422,
				"roleName":"人力总监",
				"systemId":"8",
				"systemShortName":"HR系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0067",
				"roleId":10436,
				"roleName":"凤媛01",
				"systemId":"8",
				"systemShortName":"HR系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0049",
				"roleId":10387,
				"roleName":"保险|渠道pco",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0005",
				"roleId":10154,
				"roleName":"技术组",
				"systemId":"11",
				"systemShortName":"电商支撑运营系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10398,
				"roleName":"超级管理员",
				"systemId":"35",
				"systemShortName":"电商平台运营系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0004",
				"roleId":10005,
				"roleName":"保险|理赔管理",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0022",
				"roleId":10190,
				"roleName":"保险|子管理员",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10001,
				"roleName":"系统管理员",
				"systemId":"1",
				"systemShortName":"基础管理平台",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10335,
				"roleName":"系统管理员",
				"systemId":"19",
				"systemShortName":"营销平台",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0009",
				"roleId":10911,
				"roleName":"总部员工",
				"systemId":"70",
				"systemShortName":"掌上中和",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0017",
				"roleId":10919,
				"roleName":"中/高级管理层",
				"systemId":"70",
				"systemShortName":"掌上中和",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0012",
				"roleId":10914,
				"roleName":"信息技术部",
				"systemId":"70",
				"systemShortName":"掌上中和",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0038",
				"roleId":10938,
				"roleName":"全量数据查看",
				"systemId":"70",
				"systemShortName":"掌上中和",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0004",
				"roleId":4,
				"roleName":"总部普通员工",
				"systemId":"2",
				"systemShortName":"运营管理平台",
				"userRoleType":2
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0017",
				"roleId":17,
				"roleName":"数字技术中心",
				"systemId":"2",
				"systemShortName":"运营管理平台",
				"userRoleType":2
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0009",
				"roleId":10031,
				"roleName":"总部员工",
				"systemId":"6",
				"systemShortName":"信贷管理系统",
				"userRoleType":2
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0038",
				"roleId":10111,
				"roleName":"全量数据查看",
				"systemId":"6",
				"systemShortName":"信贷管理系统",
				"userRoleType":2
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0014",
				"roleId":10138,
				"roleName":"普通员工",
				"systemId":"8",
				"systemShortName":"HR系统",
				"userRoleType":2
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0002",
				"roleId":10003,
				"roleName":"保险|业务管理",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":2
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0046",
				"roleId":10627,
				"roleName":"ERP-库存管理",
				"systemId":"11",
				"systemShortName":"电商支撑运营系统",
				"userRoleType":2
			}
		],
		"serviceType":0,
		"updateTime":*************,
		"userAccount":"***********",
		"userAdminId":"**********",
		"userAdminName":"吴玲",
		"userEmail":"",
		"userId":1939,
		"userMasterId":"0",
		"userMasterName":"",
		"userPartTimerList":null,
		"userSex":1,
		"userStatus":1
	},
	"errorCode":"",
	"errorContext":null,
	"errorMsg":"",
	"message":"",
	"success":true
}
2025-07-07 10:51:28.574 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] <--- END HTTP (13889-byte body)
2025-07-07 10:51:28.657 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] ---> GET http://insurance-admin.tsg.cfpamf.com/back/data/auth/dataAuth?authorization=eyJhbGciOiJIUzUxMiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NshtIKKX-662IIkxaqRT3Z2cDCPsxMxoHlG-p6mfclCEoh_CDU2yylpAlM64rTMlkA7Kg2RMCAPBtzWxq6_N5g HTTP/1.1
2025-07-07 10:51:28.658 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] ---> END HTTP (0-byte body)
2025-07-07 10:51:28.896 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] <--- HTTP/1.1 200 OK (237ms)
2025-07-07 10:51:28.896 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] content-type: application/json;charset=UTF-8
2025-07-07 10:51:28.897 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] date: Mon, 07 Jul 2025 02:51:29 GMT
2025-07-07 10:51:28.898 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] server: istio-envoy
2025-07-07 10:51:28.899 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] transfer-encoding: chunked
2025-07-07 10:51:28.900 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] x-envoy-upstream-service-time: 93
2025-07-07 10:51:28.900 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] 
2025-07-07 10:51:28.901 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] {"code":"0","traceId":"","message":"success","data":{"userId":null,"agentId":null,"regionName":null,"regionCode":null,"orgName":null,"orgCode":null,"orgAdmin":null,"channel":null,"picRole":null,"safeCenter":null,"branchBuName":null,"branchBuCode":null,"zoneName":null,"zoneCode":null,"orgPath":null,"branch":false},"success":true,"exception":null}
2025-07-07 10:51:28.901 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] <--- END HTTP (347-byte body)
2025-07-07 10:51:28.933 [] INFO [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.advice.GlobalExceptionHandler[37] 业务正常校验异常
com.cfpamf.common.ms.exception.MSBizNormalException: pt不合法：20250616
	at com.cfpamf.ms.insur.report.pojo.query.AssistantSalerRankQuery.getPt(AssistantSalerRankQuery.java:57)
	at com.cfpamf.ms.insur.report.service.assistant.impl.AssistantSalerServiceImpl.buildAssessConvertRankCacheKey(AssistantSalerServiceImpl.java:392)
	at com.cfpamf.ms.insur.report.service.assistant.impl.AssistantSalerServiceImpl.pageAssessConvertRank(AssistantSalerServiceImpl.java:224)
	at com.cfpamf.ms.insur.report.web.AssistantSalerNewController.pageEmpAssessConvertRankMonth(AssistantSalerNewController.java:68)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:189)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:102)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:800)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1038)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:665)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.cfpamf.ms.insur.report.advice.RequestFilter.doFilter(RequestFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:123)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:151)
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:81)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-07 10:52:22.025 [] INFO [Thread-17] org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor[208] Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-07 10:52:23.200 [] INFO [Thread-17] com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor[288] class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-07-07 10:52:23.202 [] INFO [Thread-17] com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor[288] class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-07-07 10:52:31.516 [] INFO [main] com.alibaba.boot.nacos.config.util.NacosConfigPropertiesUtils[47] nacosConfigProperties : NacosConfigProperties{serverAddr='http://mse-b81bd222-nacos-ans.mse.aliyuncs.com', contextPath='null', encode='null', endpoint='null', namespace='612ffc01-c1b8-4698-9a3d-2d4f9404694f', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='application,insurance-report.yml', group='safes', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=true}}
2025-07-07 10:52:32.644 [] INFO [main] com.alibaba.boot.nacos.config.util.NacosConfigPropertiesUtils[47] nacosConfigProperties : NacosConfigProperties{serverAddr='http://mse-b81bd222-nacos-ans.mse.aliyuncs.com', contextPath='null', encode='null', endpoint='null', namespace='612ffc01-c1b8-4698-9a3d-2d4f9404694f', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='application,insurance-report.yml', group='safes', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=true}}
2025-07-07 10:52:32.645 [] INFO [main] com.cfpamf.ms.insur.report.InsuranceReportBizApplication[675] No active profile set, falling back to default profiles: default
2025-07-07 10:52:35.185 [] INFO [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate[244] Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-07 10:52:35.191 [] INFO [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate[126] Bootstrapping Spring Data repositories in DEFAULT mode.
2025-07-07 10:52:35.290 [] INFO [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate[182] Finished Spring Data repository scanning in 72ms. Found 0 repository interfaces.
2025-07-07 10:52:36.005 [] INFO [main] org.springframework.cloud.context.scope.GenericScope[295] BeanFactory id=323dedff-86ed-336d-bca0-80ceef4b44c5
2025-07-07 10:52:36.045 [] INFO [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor[48] Post-processing PropertySource instances
2025-07-07 10:52:36.130 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-07-07 10:52:36.131 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-07-07 10:52:36.131 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-07-07 10:52:36.132 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource systemProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 10:52:36.132 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 10:52:36.133 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-07 10:52:36.133 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource applicationConfig: [classpath:/application.properties] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 10:52:36.133 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 10:52:36.133 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource application|safes|612ffc01-c1b8-4698-9a3d-2d4f9404694f||http://mse-b81bd222-nacos-ans.mse.aliyuncs.com||||||| [com.alibaba.nacos.spring.core.env.NacosPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 10:52:36.133 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource insurance-report.yml|safes|612ffc01-c1b8-4698-9a3d-2d4f9404694f||http://mse-b81bd222-nacos-ans.mse.aliyuncs.com||||||| [com.alibaba.nacos.spring.core.env.NacosPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 10:52:36.133 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource applicationConfig: [classpath:/bootstrap.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 10:52:36.133 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-07 10:52:36.560 [] INFO [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[330] Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$ccd9b9db] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-07 10:52:37.182 [] INFO [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver[34] Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-07 10:52:37.187 [] INFO [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector[31] Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-07 10:52:37.941 [] INFO [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer[90] Tomcat initialized with port(s): 10120 (http)
2025-07-07 10:52:37.977 [] INFO [main] org.apache.coyote.http11.Http11NioProtocol[173] Initializing ProtocolHandler ["http-nio-10120"]
2025-07-07 10:52:37.997 [] INFO [main] org.apache.catalina.core.StandardService[173] Starting service [Tomcat]
2025-07-07 10:52:37.998 [] INFO [main] org.apache.catalina.core.StandardEngine[173] Starting Servlet engine: [Apache Tomcat/9.0.16]
2025-07-07 10:52:38.022 [] INFO [main] org.apache.catalina.core.AprLifecycleListener[173] The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.]
2025-07-07 10:52:38.325 [] INFO [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/][173] Initializing Spring embedded WebApplicationContext
2025-07-07 10:52:38.325 [] INFO [main] org.springframework.web.context.ContextLoader[296] Root WebApplicationContext: initialization completed in 5612 ms
2025-07-07 10:52:39.947 [] DEBUG [main] com.cfpamf.ms.insur.report.config.RedisConfig[65] 自定义RedisCacheManager加载完成
2025-07-07 10:52:40.067 [] INFO [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor[342] Inconsistent constructor declaration on bean with name 'dataSourceDwConfig': single autowire-marked constructor flagged as optional - this constructor is effectively required since there is no default constructor to fall back to: public com.cfpamf.ms.insur.report.config.DataSourceDwConfig$$EnhancerBySpringCGLIB$$80885403(org.mybatis.spring.boot.autoconfigure.MybatisProperties)
2025-07-07 10:52:40.490 [] INFO [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor[342] Inconsistent constructor declaration on bean with name 'dataSourceOdpsConfig': single autowire-marked constructor flagged as optional - this constructor is effectively required since there is no default constructor to fall back to: public com.cfpamf.ms.insur.report.config.DataSourceOdpsConfig$$EnhancerBySpringCGLIB$$dd647e68(org.mybatis.spring.boot.autoconfigure.MybatisProperties)
2025-07-07 10:52:40.589 [] INFO [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor[342] Inconsistent constructor declaration on bean with name 'dataSourceSafesConfig': single autowire-marked constructor flagged as optional - this constructor is effectively required since there is no default constructor to fall back to: public com.cfpamf.ms.insur.report.config.DataSourceSafesConfig$$EnhancerBySpringCGLIB$$9b8e617a(org.mybatis.spring.boot.autoconfigure.MybatisProperties)
2025-07-07 10:52:40.656 [] INFO [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor[342] Inconsistent constructor declaration on bean with name 'dataSourceSafesPgConfig': single autowire-marked constructor flagged as optional - this constructor is effectively required since there is no default constructor to fall back to: public com.cfpamf.ms.insur.report.config.DataSourceSafesPgConfig$$EnhancerBySpringCGLIB$$46997831(org.mybatis.spring.boot.autoconfigure.MybatisProperties)
2025-07-07 10:52:44.180 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] classicInsuranceAmountSummaryService支持的诊断类型为CLASSIC_INSURANCE_AMOUNT_SUMMARY,加载成功！
2025-07-07 10:52:44.182 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] diagnosisAndConclusionService支持的诊断类型为DIAGNOSIS_AND_CONCLUSION,加载成功！
2025-07-07 10:52:44.182 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] insuranceAmountRateSummaryService支持的诊断类型为INSURANCE_AMOUNT_RATE_SUMMARY,加载成功！
2025-07-07 10:52:44.183 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] retentionRateSummaryService支持的诊断类型为RETENTION_RATE_SUMMARY,加载成功！
2025-07-07 10:52:44.669 [] DEBUG [main] com.cfpamf.ms.insur.report.config.RedisConfig[77] 自定义RedisTemplate加载完成
2025-07-07 10:52:45.779 [] INFO [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver[59] Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-07 10:52:46.486 [] INFO [main] io.lettuce.core.EpollProvider[68] Starting without optional epoll library
2025-07-07 10:52:46.488 [] INFO [main] io.lettuce.core.KqueueProvider[70] Starting without optional kqueue library
2025-07-07 10:52:47.035 [] INFO [main] springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping[69] Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2025-07-07 10:52:47.290 [] INFO [main] com.netflix.config.sources.URLConfigurationSource[122] To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-07 10:52:47.315 [] INFO [main] com.netflix.config.sources.URLConfigurationSource[122] To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-07 10:52:48.274 [] INFO [main] org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor[171] Initializing ExecutorService 'applicationTaskExecutor'
2025-07-07 10:52:49.379 [] INFO [main] tk.mybatis.mapper.autoconfigure.MapperCacheDisabler[60] Clear tk.mybatis.mapper.util.MsUtil CLASS_CACHE cache.
2025-07-07 10:52:49.380 [] INFO [main] tk.mybatis.mapper.autoconfigure.MapperCacheDisabler[60] Clear tk.mybatis.mapper.genid.GenIdUtil CACHE cache.
2025-07-07 10:52:49.382 [] INFO [main] tk.mybatis.mapper.autoconfigure.MapperCacheDisabler[60] Clear tk.mybatis.mapper.version.VersionUtil CACHE cache.
2025-07-07 10:52:49.385 [] INFO [main] tk.mybatis.mapper.autoconfigure.MapperCacheDisabler[83] Clear EntityHelper entityTableMap cache.
2025-07-07 10:52:51.616 [] INFO [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper[160] Context refreshed
2025-07-07 10:52:51.661 [] INFO [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper[163] Found 1 custom documentation plugin(s)
2025-07-07 10:52:51.725 [] INFO [main] springfox.documentation.spring.web.scanners.ApiListingReferenceScanner[41] Scanning for api listing references
2025-07-07 10:52:53.497 [] INFO [main] org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor[295] No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-07-07 10:52:53.530 [] INFO [main] org.apache.coyote.http11.Http11NioProtocol[173] Starting ProtocolHandler ["http-nio-10120"]
2025-07-07 10:52:53.566 [] INFO [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer[204] Tomcat started on port(s): 10120 (http) with context path ''
2025-07-07 10:52:53.574 [] INFO [main] com.cfpamf.ms.insur.report.InsuranceReportBizApplication[59] Started InsuranceReportBizApplication in 23.898 seconds (JVM running for 24.844)
2025-07-07 11:01:42.404 [] INFO [http-nio-10120-exec-1] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/][173] Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 11:01:42.405 [] INFO [http-nio-10120-exec-1] org.springframework.web.servlet.DispatcherServlet[524] Initializing Servlet 'dispatcherServlet'
2025-07-07 11:01:42.451 [] INFO [http-nio-10120-exec-1] org.springframework.web.servlet.DispatcherServlet[546] Completed initialization in 46 ms
2025-07-07 11:01:42.690 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] ---> GET http://bms-service.tsg.cfpamf.com/user/detail/safes HTTP/1.1
2025-07-07 11:01:42.691 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] authorization: eyJhbGciOiJIUzUxMiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NshtIKKX-662IIkxaqRT3Z2cDCPsxMxoHlG-p6mfclCEoh_CDU2yylpAlM64rTMlkA7Kg2RMCAPBtzWxq6_N5g
2025-07-07 11:01:42.691 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] ---> END HTTP (0-byte body)
2025-07-07 11:01:42.896 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] <--- HTTP/1.1 200 OK (204ms)
2025-07-07 11:01:42.897 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] content-length: 13889
2025-07-07 11:01:42.898 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] content-type: application/json
2025-07-07 11:01:42.898 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] date: Mon, 07 Jul 2025 03:01:43 GMT
2025-07-07 11:01:42.899 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] server: istio-envoy
2025-07-07 11:01:42.900 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] x-application-context: bms-service-biz:test:10027
2025-07-07 11:01:42.900 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] x-envoy-upstream-service-time: 69
2025-07-07 11:01:42.901 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] 
2025-07-07 11:01:42.926 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] {
	"code":"",
	"data":{
		"areaOrgCode":"",
		"batchNo":"20220714005",
		"birthday":-209606400000,
		"createMethod":"hr",
		"description":"",
		"deviceId":"",
		"employeeId":14,
		"employeeName":"何国强",
		"employeeStatus":3,
		"employeeType":0,
		"entryDate":1651161600000,
		"entrySystemDate":1524196438000,
		"gray":0,
		"hrOrgId":447942,
		"hrOrgTreePath":"*********/282717/360674/447942",
		"hrPostId":156932,
		"hrUserId":107369318,
		"idCard":"220881196305120848",
		"isHeadOrg":true,
		"jianzhiJobNumber":"",
		"jobCode":"**********",
		"jobNumber":"CNBJ0394",
		"lastWorkDate":null,
		"mobile":"***********",
		"officeTel":"",
		"orgCode":"204",
		"orgId":669,
		"orgName":"财务部",
		"passwordLevel":2,
		"postId":221,
		"postList":[],
		"postName":"数据开发岗",
		"purpose":"",
		"roleList":[
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0012",
				"roleId":10044,
				"roleName":"信息技术部",
				"systemId":"6",
				"systemShortName":"信贷管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0009",
				"roleId":10031,
				"roleName":"总部员工",
				"systemId":"6",
				"systemShortName":"信贷管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0038",
				"roleId":10111,
				"roleName":"全量数据查看",
				"systemId":"6",
				"systemShortName":"信贷管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0017",
				"roleId":10033,
				"roleName":"中/高级管理层",
				"systemId":"6",
				"systemShortName":"信贷管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0003",
				"roleId":3,
				"roleName":"总部职能部门负责人",
				"systemId":"2",
				"systemShortName":"运营管理平台",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10160,
				"roleName":"系统管理员",
				"systemId":"11",
				"systemShortName":"电商支撑运营系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10160,
				"roleName":"系统管理员",
				"systemId":"11",
				"systemShortName":"电商支撑运营系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R1000",
				"roleId":10002,
				"roleName":"保险|管理员",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R1000",
				"roleId":10002,
				"roleName":"保险|管理员",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10381,
				"roleName":"保险|管理员新",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10381,
				"roleName":"保险|管理员新",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10125,
				"roleName":"系统超级管理员",
				"systemId":"8",
				"systemShortName":"HR系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0066",
				"roleId":10422,
				"roleName":"人力总监",
				"systemId":"8",
				"systemShortName":"HR系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0067",
				"roleId":10436,
				"roleName":"凤媛01",
				"systemId":"8",
				"systemShortName":"HR系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0049",
				"roleId":10387,
				"roleName":"保险|渠道pco",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0005",
				"roleId":10154,
				"roleName":"技术组",
				"systemId":"11",
				"systemShortName":"电商支撑运营系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10398,
				"roleName":"超级管理员",
				"systemId":"35",
				"systemShortName":"电商平台运营系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0004",
				"roleId":10005,
				"roleName":"保险|理赔管理",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0022",
				"roleId":10190,
				"roleName":"保险|子管理员",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10001,
				"roleName":"系统管理员",
				"systemId":"1",
				"systemShortName":"基础管理平台",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10335,
				"roleName":"系统管理员",
				"systemId":"19",
				"systemShortName":"营销平台",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0009",
				"roleId":10911,
				"roleName":"总部员工",
				"systemId":"70",
				"systemShortName":"掌上中和",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0017",
				"roleId":10919,
				"roleName":"中/高级管理层",
				"systemId":"70",
				"systemShortName":"掌上中和",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0012",
				"roleId":10914,
				"roleName":"信息技术部",
				"systemId":"70",
				"systemShortName":"掌上中和",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0038",
				"roleId":10938,
				"roleName":"全量数据查看",
				"systemId":"70",
				"systemShortName":"掌上中和",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0004",
				"roleId":4,
				"roleName":"总部普通员工",
				"systemId":"2",
				"systemShortName":"运营管理平台",
				"userRoleType":2
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0017",
				"roleId":17,
				"roleName":"数字技术中心",
				"systemId":"2",
				"systemShortName":"运营管理平台",
				"userRoleType":2
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0009",
				"roleId":10031,
				"roleName":"总部员工",
				"systemId":"6",
				"systemShortName":"信贷管理系统",
				"userRoleType":2
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0038",
				"roleId":10111,
				"roleName":"全量数据查看",
				"systemId":"6",
				"systemShortName":"信贷管理系统",
				"userRoleType":2
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0014",
				"roleId":10138,
				"roleName":"普通员工",
				"systemId":"8",
				"systemShortName":"HR系统",
				"userRoleType":2
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0002",
				"roleId":10003,
				"roleName":"保险|业务管理",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":2
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0046",
				"roleId":10627,
				"roleName":"ERP-库存管理",
				"systemId":"11",
				"systemShortName":"电商支撑运营系统",
				"userRoleType":2
			}
		],
		"serviceType":0,
		"updateTime":*************,
		"userAccount":"***********",
		"userAdminId":"**********",
		"userAdminName":"吴玲",
		"userEmail":"",
		"userId":1939,
		"userMasterId":"0",
		"userMasterName":"",
		"userPartTimerList":null,
		"userSex":1,
		"userStatus":1
	},
	"errorCode":"",
	"errorContext":null,
	"errorMsg":"",
	"message":"",
	"success":true
}
2025-07-07 11:01:42.927 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] <--- END HTTP (13889-byte body)
2025-07-07 11:01:43.008 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] ---> GET http://insurance-admin.tsg.cfpamf.com/back/data/auth/dataAuth?authorization=eyJhbGciOiJIUzUxMiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NshtIKKX-662IIkxaqRT3Z2cDCPsxMxoHlG-p6mfclCEoh_CDU2yylpAlM64rTMlkA7Kg2RMCAPBtzWxq6_N5g HTTP/1.1
2025-07-07 11:01:43.008 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] ---> END HTTP (0-byte body)
2025-07-07 11:01:43.294 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] <--- HTTP/1.1 200 OK (285ms)
2025-07-07 11:01:43.295 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] content-type: application/json;charset=UTF-8
2025-07-07 11:01:43.295 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] date: Mon, 07 Jul 2025 03:01:43 GMT
2025-07-07 11:01:43.296 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] server: istio-envoy
2025-07-07 11:01:43.297 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] transfer-encoding: chunked
2025-07-07 11:01:43.297 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] x-envoy-upstream-service-time: 152
2025-07-07 11:01:43.298 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] 
2025-07-07 11:01:43.299 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] {"code":"0","traceId":"","message":"success","data":{"userId":null,"agentId":null,"regionName":null,"regionCode":null,"orgName":null,"orgCode":null,"orgAdmin":null,"channel":null,"picRole":null,"safeCenter":null,"branchBuName":null,"branchBuCode":null,"zoneName":null,"zoneCode":null,"orgPath":null,"branch":false},"success":true,"exception":null}
2025-07-07 11:01:43.300 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] <--- END HTTP (347-byte body)
2025-07-07 11:01:45.026 [] INFO [http-nio-10120-exec-1] com.alibaba.druid.pool.DruidDataSource[930] {dataSource-1} inited
2025-07-07 11:01:45.146 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpMarketingProgressDfpMapper.listAssessConvertRankCountry_COUNT[159] ==>  Preparing: WITH amt_rank AS (SELECT emp_name, emp_code, bch_name, bch_code, district_name, district_code, area_name, area_code, sy_assess_convert_insurance_amt, RANK() OVER (ORDER BY sy_assess_convert_insurance_amt DESC) rank_in_country FROM report.ads_insurance_emp_marketing_progress_dfp WHERE pt = ? AND (leave_date IS NULL OR TO_DATE(leave_date, 'YYYY-MM-DD') > TO_DATE(?, 'YYYYMMDD'))) SELECT count(0) FROM amt_rank 
2025-07-07 11:01:45.170 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpMarketingProgressDfpMapper.listAssessConvertRankCountry_COUNT[159] ==> Parameters: 20250616(String), 20250616(String)
2025-07-07 11:01:45.248 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpMarketingProgressDfpMapper.listAssessConvertRankCountry_COUNT[159] <==      Total: 1
2025-07-07 11:01:45.256 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpMarketingProgressDfpMapper.listAssessConvertRankCountry[159] ==>  Preparing: with amt_rank as ( select emp_name, emp_code, bch_name, bch_code, district_name, district_code, area_name, area_code, sy_assess_convert_insurance_amt, RANK() over (order by sy_assess_convert_insurance_amt desc) rank_in_country from report.ads_insurance_emp_marketing_progress_dfp where pt=? and (leave_date is null or TO_DATE(leave_date, 'YYYY-MM-DD') > TO_DATE(?, 'YYYYMMDD')) ) select emp_name, emp_code, bch_name, bch_code, district_name, district_code, area_name, area_code, sy_assess_convert_insurance_amt, rank_in_country from amt_rank order by rank_in_country asc LIMIT ? 
2025-07-07 11:01:45.258 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpMarketingProgressDfpMapper.listAssessConvertRankCountry[159] ==> Parameters: 20250616(String), 20250616(String), 10(Integer)
2025-07-07 11:01:45.315 [] DEBUG [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpMarketingProgressDfpMapper.listAssessConvertRankCountry[159] <==      Total: 10
2025-07-07 11:01:45.474 [] INFO [http-nio-10120-exec-1] com.cfpamf.ms.insur.report.service.assistant.impl.AssistantSalerServiceImpl[445] 缓存排名数据成功: assistant:rank:assessConvert:20250616:YEAR:EMP:1:10, 过期时间: 46695秒
2025-07-07 11:19:54.575 [] INFO [Thread-17] org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor[208] Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-07 11:19:54.669 [] INFO [Thread-17] com.alibaba.druid.pool.DruidDataSource[1825] {dataSource-1} closed
2025-07-07 11:19:55.888 [] INFO [Thread-17] com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor[288] class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-07-07 11:19:55.891 [] INFO [Thread-17] com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor[288] class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
