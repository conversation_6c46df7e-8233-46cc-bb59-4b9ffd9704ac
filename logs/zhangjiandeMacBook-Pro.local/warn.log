2025-06-17 11:15:40.305 [] WARN [main] org.mybatis.spring.mapper.ClassPathMapperScanner[202] Cannot use both: sqlSessionTemplate and sqlSessionFactory together. sqlSessionFactory is ignored.
2025-06-17 11:15:40.306 [] WARN [main] org.mybatis.spring.mapper.ClassPathMapperScanner[202] Cannot use both: sqlSessionTemplate and sqlSessionFactory together. sqlSessionFactory is ignored.
2025-06-17 11:15:40.307 [] WARN [main] org.mybatis.spring.mapper.ClassPathMapperScanner[202] Cannot use both: sqlSessionTemplate and sqlSessionFactory together. sqlSessionFactory is ignored.
2025-06-17 11:15:40.308 [] WARN [main] org.mybatis.spring.mapper.ClassPathMapperScanner[202] Cannot use both: sqlSessionTemplate and sqlSessionFactory together. sqlSessionFactory is ignored.
2025-06-17 11:15:40.310 [] WARN [main] org.mybatis.spring.mapper.ClassPathMapperScanner[202] Cannot use both: sqlSessionTemplate and sqlSessionFactory together. sqlSessionFactory is ignored.
2025-06-17 11:15:41.565 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'smDcReportMapper' and 'com.cfpamf.ms.insur.report.dao.dw.SmDcReportMapper' mapperInterface. Bean already defined with the same name!
2025-06-17 11:15:41.566 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'testDwMapper' and 'com.cfpamf.ms.insur.report.dao.dw.TestDwMapper' mapperInterface. Bean already defined with the same name!
2025-06-17 11:15:41.567 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'wxDcReportMapper' and 'com.cfpamf.ms.insur.report.dao.dw.WxDcReportMapper' mapperInterface. Bean already defined with the same name!
2025-06-17 11:15:41.567 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'lookBackMapper' and 'com.cfpamf.ms.insur.report.dao.odps.LookBackMapper' mapperInterface. Bean already defined with the same name!
2025-06-17 11:15:41.568 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'pageTraceMapper' and 'com.cfpamf.ms.insur.report.dao.odps.PageTraceMapper' mapperInterface. Bean already defined with the same name!
2025-06-17 11:15:41.568 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceAreaMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceAreaMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-06-17 11:15:41.568 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceAreaOnlinePromotionDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceAreaOnlinePromotionDfpMapper' mapperInterface. Bean already defined with the same name!
2025-06-17 11:15:41.569 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceBchMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceBchMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-06-17 11:15:41.569 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceBchOnlinePromotionDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceBchOnlinePromotionDfpMapper' mapperInterface. Bean already defined with the same name!
2025-06-17 11:15:41.570 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceCfpamfMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceCfpamfMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-06-17 11:15:41.570 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceDistrictMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceDistrictMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-06-17 11:15:41.571 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceDistrictOnlinePromotionDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceDistrictOnlinePromotionDfpMapper' mapperInterface. Bean already defined with the same name!
2025-06-17 11:15:41.571 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceEmpMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-06-17 11:15:41.572 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceEmpOnlinePromotionDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpOnlinePromotionDfpMapper' mapperInterface. Bean already defined with the same name!
2025-06-17 11:15:41.572 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'assistantAdminMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AssistantAdminMapper' mapperInterface. Bean already defined with the same name!
2025-06-17 11:15:41.576 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'dwaSafesPhoenixTodoBchMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.DwaSafesPhoenixTodoBchMapper' mapperInterface. Bean already defined with the same name!
2025-06-17 11:15:41.578 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'dwaSafesPhoenixTodoEmpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.DwaSafesPhoenixTodoEmpMapper' mapperInterface. Bean already defined with the same name!
2025-06-17 11:15:41.583 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'smSafepgReportMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.SmSafepgReportMapper' mapperInterface. Bean already defined with the same name!
2025-06-17 11:15:41.583 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'tempSafesStatsNewYearMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.TempSafesStatsNewYearMapper' mapperInterface. Bean already defined with the same name!
2025-06-17 11:15:41.584 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'assistantCalcConfigParamsMapper' and 'com.cfpamf.ms.insur.report.dao.safes.AssistantCalcConfigParamsMapper' mapperInterface. Bean already defined with the same name!
2025-06-17 11:15:41.584 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'bizTargetMapper' and 'com.cfpamf.ms.insur.report.dao.safes.BizTargetMapper' mapperInterface. Bean already defined with the same name!
2025-06-17 11:15:41.585 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'orgPicExtraMapper' and 'com.cfpamf.ms.insur.report.dao.safes.OrgPicExtraMapper' mapperInterface. Bean already defined with the same name!
2025-06-17 11:15:41.587 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'smOrderMapper' and 'com.cfpamf.ms.insur.report.dao.safes.SmOrderMapper' mapperInterface. Bean already defined with the same name!
2025-06-17 11:15:41.587 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'userPostMapper' and 'com.cfpamf.ms.insur.report.dao.safes.UserPostMapper' mapperInterface. Bean already defined with the same name!
2025-06-17 11:15:41.588 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[146] No MyBatis mapper was found in '[com.cfpamf.ms.insur.report]' package. Please check your configuration.
2025-06-17 11:15:53.099 [] WARN [main] com.netflix.config.sources.URLConfigurationSource[121] No URLs will be polled as dynamic configuration sources.
2025-06-17 11:15:53.112 [] WARN [main] com.netflix.config.sources.URLConfigurationSource[121] No URLs will be polled as dynamic configuration sources.
2025-06-17 11:15:55.455 [] WARN [main] org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration[70] Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-06-17 11:16:30.772 [] ERROR [http-nio-10120-exec-5] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].[dispatcherServlet][175] Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.IllegalStateException: Required request attribute 'org.springframework.web.servlet.HandlerMapping.pathWithinHandlerMapping' is not set] with root cause
java.lang.IllegalStateException: Required request attribute 'org.springframework.web.servlet.HandlerMapping.pathWithinHandlerMapping' is not set
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.getResource(ResourceHttpRequestHandler.java:511)
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:451)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:53)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1038)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:897)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:645)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.cfpamf.ms.insur.report.advice.RequestFilter.doFilter(RequestFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:123)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:151)
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:81)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-06-17 11:19:22.442 [] ERROR [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.advice.GlobalExceptionHandler[136] 服务器内部异常
org.springframework.dao.DataIntegrityViolationException: 
### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: invalid value "N" for "YYYY"
  详细：Value must be an integer.
### The error may exist in file [/Users/<USER>/IdeaProjects/insuance-report/insurance-report-biz/target/classes/mapper/safepg/AdsInsuranceEmpMarketingProgressDfpMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: with amt_rank as (         select         emp_name, emp_code, bch_name, bch_code, district_name, district_code, area_name, area_code,                    sm_assess_convert_insurance_amt,          RANK() over (order by                    sm_assess_convert_insurance_amt          desc) rank_in_country         from report.ads_insurance_emp_marketing_progress_dfp where pt=?             and (leave_date is null or TO_DATE(leave_date, 'YYYY-MM-DD') > TO_DATE(?, 'YYYYMMDD'))         )         select         emp_name, emp_code, bch_name, bch_code, district_name, district_code, area_name, area_code,                    sm_assess_convert_insurance_amt,          rank_in_country         from amt_rank where emp_code = ?
### Cause: org.postgresql.util.PSQLException: ERROR: invalid value "N" for "YYYY"
  详细：Value must be an integer.
; ERROR: invalid value "N" for "YYYY"
  详细：Value must be an integer.; nested exception is org.postgresql.util.PSQLException: ERROR: invalid value "N" for "YYYY"
  详细：Value must be an integer.
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:73)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:446)
	at com.sun.proxy.$Proxy161.selectOne(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:166)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:83)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:59)
	at com.sun.proxy.$Proxy171.queryRankCountry(Unknown Source)
	at com.cfpamf.ms.insur.report.service.assistant.impl.AssistantSalerServiceImpl.getAssistantSalerRank(AssistantSalerServiceImpl.java:100)
	at com.cfpamf.ms.insur.report.web.AssistantSalerController.empRankMonth(AssistantSalerController.java:87)
	at com.cfpamf.ms.insur.report.web.AssistantSalerController$$FastClassBySpringCGLIB$$310476c6.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:749)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:56)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:93)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.cfpamf.ms.insur.report.web.AssistantSalerController$$EnhancerBySpringCGLIB$$270093da.empRankMonth(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:189)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:102)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:800)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1038)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:897)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:645)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.cfpamf.ms.insur.report.advice.RequestFilter.doFilter(RequestFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:123)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:151)
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:81)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.postgresql.util.PSQLException: ERROR: invalid value "N" for "YYYY"
  详细：Value must be an integer.
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2440)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2183)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:308)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:441)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:365)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:143)
	at org.postgresql.jdbc.PgPreparedStatement.execute(PgPreparedStatement.java:132)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:498)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy254.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:63)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:326)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:111)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy252.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:148)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:141)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:77)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:433)
	... 90 common frames omitted
2025-07-07 09:25:19.574 [] WARN [main] org.mybatis.spring.mapper.ClassPathMapperScanner[202] Cannot use both: sqlSessionTemplate and sqlSessionFactory together. sqlSessionFactory is ignored.
2025-07-07 09:25:19.576 [] WARN [main] org.mybatis.spring.mapper.ClassPathMapperScanner[202] Cannot use both: sqlSessionTemplate and sqlSessionFactory together. sqlSessionFactory is ignored.
2025-07-07 09:25:19.577 [] WARN [main] org.mybatis.spring.mapper.ClassPathMapperScanner[202] Cannot use both: sqlSessionTemplate and sqlSessionFactory together. sqlSessionFactory is ignored.
2025-07-07 09:25:20.108 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'smDcReportMapper' and 'com.cfpamf.ms.insur.report.dao.dw.SmDcReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:25:20.108 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'testDwMapper' and 'com.cfpamf.ms.insur.report.dao.dw.TestDwMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:25:20.108 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'wxDcReportMapper' and 'com.cfpamf.ms.insur.report.dao.dw.WxDcReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:25:20.109 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'lookBackMapper' and 'com.cfpamf.ms.insur.report.dao.odps.LookBackMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:25:20.109 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'pageTraceMapper' and 'com.cfpamf.ms.insur.report.dao.odps.PageTraceMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:25:20.110 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceAreaMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceAreaMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:25:20.110 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceAreaOnlinePromotionDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceAreaOnlinePromotionDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:25:20.111 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceBchMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceBchMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:25:20.112 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceBchOnlinePromotionDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceBchOnlinePromotionDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:25:20.113 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceCfpamfMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceCfpamfMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:25:20.113 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceDistrictMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceDistrictMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:25:20.113 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceDistrictOnlinePromotionDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceDistrictOnlinePromotionDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:25:20.114 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceEmpMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:25:20.114 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceEmpOnlinePromotionDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpOnlinePromotionDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:25:20.115 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'assistantAdminMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AssistantAdminMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:25:20.115 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'dwaSafesPhoenixTodoEmpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.DwaSafesPhoenixTodoEmpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:25:20.116 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'smSafepgReportMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.SmSafepgReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:25:20.117 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'orgPicExtraMapper' and 'com.cfpamf.ms.insur.report.dao.safes.OrgPicExtraMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:25:20.117 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'smOrderMapper' and 'com.cfpamf.ms.insur.report.dao.safes.SmOrderMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:25:20.118 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'userPostMapper' and 'com.cfpamf.ms.insur.report.dao.safes.UserPostMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:25:20.119 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[146] No MyBatis mapper was found in '[com.cfpamf.ms.insur.report]' package. Please check your configuration.
2025-07-07 09:25:33.286 [] WARN [main] com.netflix.config.sources.URLConfigurationSource[121] No URLs will be polled as dynamic configuration sources.
2025-07-07 09:25:33.299 [] WARN [main] com.netflix.config.sources.URLConfigurationSource[121] No URLs will be polled as dynamic configuration sources.
2025-07-07 09:25:35.681 [] WARN [main] org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration[70] Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-07 09:34:11.809 [] ERROR [http-nio-10120-exec-5] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].[dispatcherServlet][175] Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.IllegalStateException: Required request attribute 'org.springframework.web.servlet.HandlerMapping.pathWithinHandlerMapping' is not set] with root cause
java.lang.IllegalStateException: Required request attribute 'org.springframework.web.servlet.HandlerMapping.pathWithinHandlerMapping' is not set
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.getResource(ResourceHttpRequestHandler.java:511)
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:451)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:53)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1038)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:897)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:645)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.cfpamf.ms.insur.report.advice.RequestFilter.doFilter(RequestFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:123)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:151)
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:81)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-07 09:53:33.125 [] ERROR [http-nio-10120-exec-8] com.cfpamf.ms.insur.report.advice.GlobalExceptionHandler[136] 服务器内部异常
java.lang.ClassCastException: com.cfpamf.ms.insur.report.pojo.query.AssistantSalerRankQuery cannot be cast to java.lang.String
	at com.cfpamf.ms.insur.report.aspect.AssistantSalerAspect.before(AssistantSalerAspect.java:30)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:626)
	at org.springframework.aop.aspectj.AspectJMethodBeforeAdvice.before(AspectJMethodBeforeAdvice.java:44)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:55)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:93)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.cfpamf.ms.insur.report.web.AssistantSalerController$$EnhancerBySpringCGLIB$$d1de58a8.pageEmpPromotionRankMonth(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:189)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:102)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:800)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1038)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:665)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.cfpamf.ms.insur.report.advice.RequestFilter.doFilter(RequestFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:123)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:151)
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:81)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-07 09:54:58.834 [] ERROR [http-nio-10120-exec-6] com.cfpamf.ms.insur.report.advice.GlobalExceptionHandler[136] 服务器内部异常
java.lang.ClassCastException: com.cfpamf.ms.insur.report.pojo.query.AssistantSalerRankQuery cannot be cast to java.lang.String
	at com.cfpamf.ms.insur.report.aspect.AssistantSalerAspect.before(AssistantSalerAspect.java:30)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:626)
	at org.springframework.aop.aspectj.AspectJMethodBeforeAdvice.before(AspectJMethodBeforeAdvice.java:44)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:55)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:93)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.cfpamf.ms.insur.report.web.AssistantSalerController$$EnhancerBySpringCGLIB$$d1de58a8.pageEmpPromotionRankMonth(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:189)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:102)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:800)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1038)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:665)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.cfpamf.ms.insur.report.advice.RequestFilter.doFilter(RequestFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:123)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:151)
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:81)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-07 09:58:36.881 [] WARN [main] org.mybatis.spring.mapper.ClassPathMapperScanner[202] Cannot use both: sqlSessionTemplate and sqlSessionFactory together. sqlSessionFactory is ignored.
2025-07-07 09:58:36.881 [] WARN [main] org.mybatis.spring.mapper.ClassPathMapperScanner[202] Cannot use both: sqlSessionTemplate and sqlSessionFactory together. sqlSessionFactory is ignored.
2025-07-07 09:58:36.881 [] WARN [main] org.mybatis.spring.mapper.ClassPathMapperScanner[202] Cannot use both: sqlSessionTemplate and sqlSessionFactory together. sqlSessionFactory is ignored.
2025-07-07 09:58:37.290 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'smDcReportMapper' and 'com.cfpamf.ms.insur.report.dao.dw.SmDcReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:58:37.290 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'testDwMapper' and 'com.cfpamf.ms.insur.report.dao.dw.TestDwMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:58:37.291 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'wxDcReportMapper' and 'com.cfpamf.ms.insur.report.dao.dw.WxDcReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:58:37.291 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'lookBackMapper' and 'com.cfpamf.ms.insur.report.dao.odps.LookBackMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:58:37.291 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'pageTraceMapper' and 'com.cfpamf.ms.insur.report.dao.odps.PageTraceMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:58:37.292 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceAreaMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceAreaMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:58:37.292 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceAreaOnlinePromotionDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceAreaOnlinePromotionDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:58:37.292 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceBchMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceBchMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:58:37.292 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceBchOnlinePromotionDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceBchOnlinePromotionDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:58:37.292 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceCfpamfMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceCfpamfMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:58:37.293 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceDistrictMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceDistrictMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:58:37.293 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceDistrictOnlinePromotionDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceDistrictOnlinePromotionDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:58:37.293 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceEmpMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:58:37.293 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceEmpOnlinePromotionDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpOnlinePromotionDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:58:37.293 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'assistantAdminMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AssistantAdminMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:58:37.294 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'dwaSafesPhoenixTodoEmpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.DwaSafesPhoenixTodoEmpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:58:37.294 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'smSafepgReportMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.SmSafepgReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:58:37.294 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'orgPicExtraMapper' and 'com.cfpamf.ms.insur.report.dao.safes.OrgPicExtraMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:58:37.294 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'smOrderMapper' and 'com.cfpamf.ms.insur.report.dao.safes.SmOrderMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:58:37.295 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'userPostMapper' and 'com.cfpamf.ms.insur.report.dao.safes.UserPostMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 09:58:37.295 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[146] No MyBatis mapper was found in '[com.cfpamf.ms.insur.report]' package. Please check your configuration.
2025-07-07 09:58:48.639 [] WARN [main] com.netflix.config.sources.URLConfigurationSource[121] No URLs will be polled as dynamic configuration sources.
2025-07-07 09:58:48.651 [] WARN [main] com.netflix.config.sources.URLConfigurationSource[121] No URLs will be polled as dynamic configuration sources.
2025-07-07 09:58:51.100 [] WARN [main] org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration[70] Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-07 10:17:07.746 [] ERROR [pool-3-thread-1] org.springframework.scheduling.support.TaskUtils$LoggingErrorHandler[96] Unexpected error occurred in scheduled task.
org.springframework.data.redis.RedisSystemException: Redis exception; nested exception is io.lettuce.core.RedisException: java.io.IOException: Connection reset by peer
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:268)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.convertLettuceAccessException(LettuceSetCommands.java:520)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.sMembers(LettuceSetCommands.java:245)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.sMembers(DefaultedRedisConnection.java:607)
	at org.springframework.data.redis.core.DefaultSetOperations.lambda$members$6(DefaultSetOperations.java:158)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:224)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:184)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:95)
	at org.springframework.data.redis.core.DefaultSetOperations.members(DefaultSetOperations.java:158)
	at org.springframework.data.redis.core.DefaultBoundSetOperations.members(DefaultBoundSetOperations.java:152)
	at org.springframework.session.data.redis.RedisSessionExpirationPolicy.cleanExpiredSessions(RedisSessionExpirationPolicy.java:132)
	at org.springframework.session.data.redis.RedisOperationsSessionRepository.cleanupExpiredSessions(RedisOperationsSessionRepository.java:430)
	at org.springframework.session.data.redis.config.annotation.web.http.RedisHttpSessionConfiguration.lambda$configureTasks$0(RedisHttpSessionConfiguration.java:248)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisException: java.io.IOException: Connection reset by peer
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:129)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:69)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:80)
	at com.sun.proxy.$Proxy197.smembers(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.sMembers(LettuceSetCommands.java:243)
	... 20 common frames omitted
Caused by: java.io.IOException: Connection reset by peer
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledUnsafeDirectByteBuf.setBytes(PooledUnsafeDirectByteBuf.java:288)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:347)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:148)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:677)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:612)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:529)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:491)
	at io.netty.util.concurrent.SingleThreadEventExecutor$5.run(SingleThreadEventExecutor.java:905)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
2025-07-07 10:29:44.391 [] WARN [Thread-17] org.springframework.data.redis.listener.RedisMessageListenerContainer[868] Unable to unsubscribe from subscriptions
io.lettuce.core.RedisException: java.io.IOException: Connection reset by peer
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:129)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:69)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:80)
	at com.sun.proxy.$Proxy226.unsubscribe(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceSubscription.doClose(LettuceSubscription.java:62)
	at org.springframework.data.redis.connection.util.AbstractSubscription.close(AbstractSubscription.java:106)
	at org.springframework.data.redis.listener.RedisMessageListenerContainer$SubscriptionTask.cancel(RedisMessageListenerContainer.java:866)
	at org.springframework.data.redis.listener.RedisMessageListenerContainer.stop(RedisMessageListenerContainer.java:225)
	at org.springframework.data.redis.listener.RedisMessageListenerContainer.stop(RedisMessageListenerContainer.java:183)
	at org.springframework.context.support.DefaultLifecycleProcessor.doStop(DefaultLifecycleProcessor.java:238)
	at org.springframework.context.support.DefaultLifecycleProcessor.access$300(DefaultLifecycleProcessor.java:53)
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.stop(DefaultLifecycleProcessor.java:377)
	at org.springframework.context.support.DefaultLifecycleProcessor.stopBeans(DefaultLifecycleProcessor.java:210)
	at org.springframework.context.support.DefaultLifecycleProcessor.onClose(DefaultLifecycleProcessor.java:128)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1018)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:945)
Caused by: java.io.IOException: Connection reset by peer
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledUnsafeDirectByteBuf.setBytes(PooledUnsafeDirectByteBuf.java:288)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:347)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:148)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:677)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:612)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:529)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:491)
	at io.netty.util.concurrent.SingleThreadEventExecutor$5.run(SingleThreadEventExecutor.java:905)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-07-07 10:30:00.083 [] WARN [main] org.mybatis.spring.mapper.ClassPathMapperScanner[202] Cannot use both: sqlSessionTemplate and sqlSessionFactory together. sqlSessionFactory is ignored.
2025-07-07 10:30:00.084 [] WARN [main] org.mybatis.spring.mapper.ClassPathMapperScanner[202] Cannot use both: sqlSessionTemplate and sqlSessionFactory together. sqlSessionFactory is ignored.
2025-07-07 10:30:00.084 [] WARN [main] org.mybatis.spring.mapper.ClassPathMapperScanner[202] Cannot use both: sqlSessionTemplate and sqlSessionFactory together. sqlSessionFactory is ignored.
2025-07-07 10:30:00.561 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'smDcReportMapper' and 'com.cfpamf.ms.insur.report.dao.dw.SmDcReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:30:00.561 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'testDwMapper' and 'com.cfpamf.ms.insur.report.dao.dw.TestDwMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:30:00.562 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'wxDcReportMapper' and 'com.cfpamf.ms.insur.report.dao.dw.WxDcReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:30:00.562 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'lookBackMapper' and 'com.cfpamf.ms.insur.report.dao.odps.LookBackMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:30:00.562 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'pageTraceMapper' and 'com.cfpamf.ms.insur.report.dao.odps.PageTraceMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:30:00.562 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceAreaMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceAreaMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:30:00.562 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceAreaOnlinePromotionDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceAreaOnlinePromotionDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:30:00.562 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceBchMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceBchMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:30:00.563 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceBchOnlinePromotionDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceBchOnlinePromotionDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:30:00.563 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceCfpamfMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceCfpamfMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:30:00.563 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceDistrictMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceDistrictMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:30:00.563 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceDistrictOnlinePromotionDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceDistrictOnlinePromotionDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:30:00.564 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceEmpMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:30:00.564 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceEmpOnlinePromotionDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpOnlinePromotionDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:30:00.564 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'assistantAdminMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AssistantAdminMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:30:00.564 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'dwaSafesPhoenixTodoEmpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.DwaSafesPhoenixTodoEmpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:30:00.565 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'smSafepgReportMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.SmSafepgReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:30:00.565 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'orgPicExtraMapper' and 'com.cfpamf.ms.insur.report.dao.safes.OrgPicExtraMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:30:00.565 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'smOrderMapper' and 'com.cfpamf.ms.insur.report.dao.safes.SmOrderMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:30:00.565 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'userPostMapper' and 'com.cfpamf.ms.insur.report.dao.safes.UserPostMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:30:00.565 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[146] No MyBatis mapper was found in '[com.cfpamf.ms.insur.report]' package. Please check your configuration.
2025-07-07 10:30:12.919 [] WARN [main] com.netflix.config.sources.URLConfigurationSource[121] No URLs will be polled as dynamic configuration sources.
2025-07-07 10:30:12.930 [] WARN [main] com.netflix.config.sources.URLConfigurationSource[121] No URLs will be polled as dynamic configuration sources.
2025-07-07 10:30:15.530 [] WARN [main] org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration[70] Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-07 10:30:46.513 [] ERROR [http-nio-10120-exec-6] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].[dispatcherServlet][175] Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.IllegalStateException: Required request attribute 'org.springframework.web.servlet.HandlerMapping.pathWithinHandlerMapping' is not set] with root cause
java.lang.IllegalStateException: Required request attribute 'org.springframework.web.servlet.HandlerMapping.pathWithinHandlerMapping' is not set
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.getResource(ResourceHttpRequestHandler.java:511)
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:451)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:53)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1038)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:897)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:645)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.cfpamf.ms.insur.report.advice.RequestFilter.doFilter(RequestFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:123)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:151)
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:81)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-07 10:47:20.194 [] WARN [main] org.mybatis.spring.mapper.ClassPathMapperScanner[202] Cannot use both: sqlSessionTemplate and sqlSessionFactory together. sqlSessionFactory is ignored.
2025-07-07 10:47:20.195 [] WARN [main] org.mybatis.spring.mapper.ClassPathMapperScanner[202] Cannot use both: sqlSessionTemplate and sqlSessionFactory together. sqlSessionFactory is ignored.
2025-07-07 10:47:20.195 [] WARN [main] org.mybatis.spring.mapper.ClassPathMapperScanner[202] Cannot use both: sqlSessionTemplate and sqlSessionFactory together. sqlSessionFactory is ignored.
2025-07-07 10:47:20.783 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'smDcReportMapper' and 'com.cfpamf.ms.insur.report.dao.dw.SmDcReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:47:20.784 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'testDwMapper' and 'com.cfpamf.ms.insur.report.dao.dw.TestDwMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:47:20.878 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'wxDcReportMapper' and 'com.cfpamf.ms.insur.report.dao.dw.WxDcReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:47:20.879 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'lookBackMapper' and 'com.cfpamf.ms.insur.report.dao.odps.LookBackMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:47:20.879 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'pageTraceMapper' and 'com.cfpamf.ms.insur.report.dao.odps.PageTraceMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:47:20.880 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceAreaMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceAreaMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:47:20.881 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceAreaOnlinePromotionDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceAreaOnlinePromotionDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:47:20.882 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceBchMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceBchMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:47:20.882 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceBchOnlinePromotionDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceBchOnlinePromotionDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:47:20.882 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceCfpamfMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceCfpamfMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:47:20.883 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceDistrictMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceDistrictMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:47:20.884 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceDistrictOnlinePromotionDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceDistrictOnlinePromotionDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:47:20.884 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceEmpMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:47:20.885 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceEmpOnlinePromotionDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpOnlinePromotionDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:47:20.885 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'assistantAdminMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AssistantAdminMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:47:20.886 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'dwaSafesPhoenixTodoEmpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.DwaSafesPhoenixTodoEmpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:47:20.886 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'smSafepgReportMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.SmSafepgReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:47:20.887 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'orgPicExtraMapper' and 'com.cfpamf.ms.insur.report.dao.safes.OrgPicExtraMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:47:20.887 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'smOrderMapper' and 'com.cfpamf.ms.insur.report.dao.safes.SmOrderMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:47:20.888 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'userPostMapper' and 'com.cfpamf.ms.insur.report.dao.safes.UserPostMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:47:20.889 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[146] No MyBatis mapper was found in '[com.cfpamf.ms.insur.report]' package. Please check your configuration.
2025-07-07 10:47:33.000 [] WARN [main] com.netflix.config.sources.URLConfigurationSource[121] No URLs will be polled as dynamic configuration sources.
2025-07-07 10:47:33.017 [] WARN [main] com.netflix.config.sources.URLConfigurationSource[121] No URLs will be polled as dynamic configuration sources.
2025-07-07 10:47:35.821 [] WARN [main] org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration[70] Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-07 10:52:34.328 [] WARN [main] org.mybatis.spring.mapper.ClassPathMapperScanner[202] Cannot use both: sqlSessionTemplate and sqlSessionFactory together. sqlSessionFactory is ignored.
2025-07-07 10:52:34.328 [] WARN [main] org.mybatis.spring.mapper.ClassPathMapperScanner[202] Cannot use both: sqlSessionTemplate and sqlSessionFactory together. sqlSessionFactory is ignored.
2025-07-07 10:52:34.328 [] WARN [main] org.mybatis.spring.mapper.ClassPathMapperScanner[202] Cannot use both: sqlSessionTemplate and sqlSessionFactory together. sqlSessionFactory is ignored.
2025-07-07 10:52:34.777 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'smDcReportMapper' and 'com.cfpamf.ms.insur.report.dao.dw.SmDcReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:52:34.778 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'testDwMapper' and 'com.cfpamf.ms.insur.report.dao.dw.TestDwMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:52:34.778 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'wxDcReportMapper' and 'com.cfpamf.ms.insur.report.dao.dw.WxDcReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:52:34.778 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'lookBackMapper' and 'com.cfpamf.ms.insur.report.dao.odps.LookBackMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:52:34.778 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'pageTraceMapper' and 'com.cfpamf.ms.insur.report.dao.odps.PageTraceMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:52:34.779 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceAreaMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceAreaMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:52:34.779 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceAreaOnlinePromotionDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceAreaOnlinePromotionDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:52:34.779 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceBchMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceBchMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:52:34.779 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceBchOnlinePromotionDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceBchOnlinePromotionDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:52:34.780 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceCfpamfMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceCfpamfMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:52:34.780 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceDistrictMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceDistrictMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:52:34.780 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceDistrictOnlinePromotionDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceDistrictOnlinePromotionDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:52:34.781 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceEmpMarketingProgressDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpMarketingProgressDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:52:34.782 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'adsInsuranceEmpOnlinePromotionDfpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpOnlinePromotionDfpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:52:34.782 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'assistantAdminMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.AssistantAdminMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:52:34.782 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'dwaSafesPhoenixTodoEmpMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.DwaSafesPhoenixTodoEmpMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:52:34.783 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'smSafepgReportMapper' and 'com.cfpamf.ms.insur.report.dao.safepg.SmSafepgReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:52:34.783 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'orgPicExtraMapper' and 'com.cfpamf.ms.insur.report.dao.safes.OrgPicExtraMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:52:34.784 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'smOrderMapper' and 'com.cfpamf.ms.insur.report.dao.safes.SmOrderMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:52:34.784 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[229] Skipping MapperFactoryBean with name 'userPostMapper' and 'com.cfpamf.ms.insur.report.dao.safes.UserPostMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:52:34.784 [] WARN [main] tk.mybatis.spring.mapper.ClassPathMapperScanner[146] No MyBatis mapper was found in '[com.cfpamf.ms.insur.report]' package. Please check your configuration.
2025-07-07 10:52:47.288 [] WARN [main] com.netflix.config.sources.URLConfigurationSource[121] No URLs will be polled as dynamic configuration sources.
2025-07-07 10:52:47.314 [] WARN [main] com.netflix.config.sources.URLConfigurationSource[121] No URLs will be polled as dynamic configuration sources.
2025-07-07 10:52:50.766 [] WARN [main] org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration[70] Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
