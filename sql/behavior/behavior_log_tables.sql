-- 用户行为日志拉取模块相关表结构

-- 1. 日志拉取任务表
CREATE TABLE IF NOT EXISTS behavior_log_task (
    id BIGSERIAL PRIMARY KEY,
    task_name VARCHAR(100) NOT NULL COMMENT '任务名称',
    project_name VARCHAR(100) NOT NULL COMMENT 'SLS项目名称',
    logstore_name VARCHAR(100) NOT NULL COMMENT 'SLS日志库名称',
    start_time TIMESTAMP NOT NULL COMMENT '日志获取开始时间',
    end_time TIMESTAMP COMMENT '日志获取结束时间',
    query_condition TEXT COMMENT '查询条件',
    task_status INTEGER NOT NULL DEFAULT 0 COMMENT '任务状态：0-未开始，1-进行中，2-已完成，3-失败',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    remark TEXT COMMENT '备注'
);

-- 创建索引
CREATE INDEX idx_behavior_log_task_project ON behavior_log_task(project_name);
CREATE INDEX idx_behavior_log_task_status ON behavior_log_task(task_status);
CREATE INDEX idx_behavior_log_task_start_time ON behavior_log_task(start_time);

-- 2. 日志拉取任务明细表
CREATE TABLE IF NOT EXISTS behavior_log_task_detail (
    id BIGSERIAL PRIMARY KEY,
    task_id BIGINT NOT NULL COMMENT '任务ID',
    shard_id INTEGER NOT NULL COMMENT '分片ID',
    cursor_value VARCHAR(500) COMMENT '当前游标值',
    next_cursor VARCHAR(500) COMMENT '下一个游标值',
    processed_count BIGINT DEFAULT 0 COMMENT '已处理日志数量',
    last_process_time TIMESTAMP COMMENT '最后处理时间',
    status INTEGER NOT NULL DEFAULT 0 COMMENT '状态：0-未开始，1-进行中，2-已完成，3-失败',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    error_message TEXT COMMENT '错误信息',
    FOREIGN KEY (task_id) REFERENCES behavior_log_task(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_behavior_log_task_detail_task_id ON behavior_log_task_detail(task_id);
CREATE INDEX idx_behavior_log_task_detail_shard ON behavior_log_task_detail(task_id, shard_id);
CREATE INDEX idx_behavior_log_task_detail_status ON behavior_log_task_detail(status);

-- 3. 用户行为日志表
CREATE TABLE IF NOT EXISTS user_behavior_log (
    id BIGSERIAL PRIMARY KEY,
    task_id BIGINT NOT NULL COMMENT '任务ID',
    user_id VARCHAR(100) COMMENT '用户ID',
    session_id VARCHAR(100) COMMENT '会话ID',
    event_type VARCHAR(50) COMMENT '事件类型',
    event_name VARCHAR(100) COMMENT '事件名称',
    page_url VARCHAR(500) COMMENT '页面URL',
    page_title VARCHAR(200) COMMENT '页面标题',
    element_id VARCHAR(100) COMMENT '元素ID',
    element_text VARCHAR(200) COMMENT '元素文本',
    event_time TIMESTAMP COMMENT '事件时间',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    device_type VARCHAR(50) COMMENT '设备类型',
    browser_type VARCHAR(50) COMMENT '浏览器类型',
    os_type VARCHAR(50) COMMENT '操作系统类型',
    screen_resolution VARCHAR(20) COMMENT '屏幕分辨率',
    referrer_url VARCHAR(500) COMMENT '来源URL',
    duration BIGINT COMMENT '持续时间(毫秒)',
    custom_attributes JSONB COMMENT '自定义属性(JSON格式)',
    raw_log_data TEXT COMMENT '原始日志数据',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (task_id) REFERENCES behavior_log_task(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_user_behavior_log_task_id ON user_behavior_log(task_id);
CREATE INDEX idx_user_behavior_log_user_id ON user_behavior_log(user_id);
CREATE INDEX idx_user_behavior_log_event_time ON user_behavior_log(event_time);
CREATE INDEX idx_user_behavior_log_event_type ON user_behavior_log(event_type);
CREATE INDEX idx_user_behavior_log_page_url ON user_behavior_log(page_url);
CREATE INDEX idx_user_behavior_log_session_id ON user_behavior_log(session_id);

-- 添加表注释
COMMENT ON TABLE behavior_log_task IS '用户行为日志拉取任务表';
COMMENT ON TABLE behavior_log_task_detail IS '用户行为日志拉取任务明细表';
COMMENT ON TABLE user_behavior_log IS '用户行为日志表';
